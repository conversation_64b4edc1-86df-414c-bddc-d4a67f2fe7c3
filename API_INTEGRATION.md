# Dashboard API Integration

## Overview
This document describes the integration of the `getSubmissionsForUserProfile` API endpoint into the dashboard page.

## API Endpoint
```
POST https://us-central1-critiqle-abd19.cloudfunctions.net/getSubmissionsForUserProfile
```

## Request Format
```json
{
  "limit": 10,
  "cursor": {
    "lastCreatedAt": {
      "_seconds": 1753180330,
      "_nanoseconds": 65000000
    }
  }
}
```

## Response Format
```json
{
  "posts": [
    {
      "id": "wyuUTCf3yfbujFWc38eo",
      "creatorId": "sdilNyR9ayeVY0M2bFgLEiYdZel1",
      "staticContentId": "3b18ff72-24e8-4391-bddb-0361ad6ed612",
      "title": "Added a new Caption",
      "visibility": "public",
      "version": 1,
      "contents": [
        {
          "id": "KEwE7fLyJOW0MaviyYGR",
          "contentType": "image",
          "contentUrl": "https://firebasestorage.googleapis.com/...",
          "thumbnailUrl": "https://firebasestorage.googleapis.com/...",
          "isMain": true,
          "uploadedAt": {
            "_seconds": 1753944487,
            "_nanoseconds": 284000000
          }
        }
      ],
      "createdAt": {
        "_seconds": 1753944487,
        "_nanoseconds": 442000000
      }
    }
  ],
  "count": 1,
  "nextCursor": {
    "_seconds": 1753944487,
    "_nanoseconds": 442000000
  },
  "hasMore": false
}
```

## Implementation Files

### 1. Types (`src/types/submission.ts`)
- `FirebaseTimestamp` - Firebase timestamp format
- `SubmissionContent` - Individual content item (image/video)
- `Submission` - Complete submission object
- `SubmissionsResponse` - API response format
- `SubmissionsRequest` - API request format

### 2. API Integration (`src/lib/axios/network-manager.ts`)
- `getUserSubmissions()` - Method to call the API endpoint

### 3. Custom Hook (`src/hooks/useSubmissions.ts`)
- `useSubmissions()` - React hook for fetching and managing submissions
- Handles pagination, loading states, and error handling

### 4. Components
- `SubmissionCard` (`src/components/dashboard/SubmissionCard.tsx`) - Individual submission card
- `SubmissionsGrid` (`src/components/dashboard/SubmissionsGrid.tsx`) - Grid layout with pagination

### 5. Dashboard Integration (`src/views/dashboard/Dashboard.tsx`)
- Integrated `SubmissionsGrid` into the submissions tab

## Features

### Card Design
- **Thumbnail Display** - Shows content thumbnail with fallback
- **Content Type Badge** - Image/Video indicator
- **Multiple Content Indicator** - Shows count if multiple files
- **Visibility Badge** - Public/Circle indicator
- **Metadata** - Title, date, time, version
- **Action Buttons** - View Details, View Feedback

### Pagination
- **Load More** - Button to load additional submissions
- **Infinite Scroll** - Can be easily implemented
- **Cursor-based** - Uses Firebase timestamp cursors

### Error Handling
- **Network Errors** - Proper error messages
- **Empty States** - No submissions placeholder
- **Retry Logic** - Retry failed requests

### Loading States
- **Initial Load** - Skeleton/spinner for first load
- **Load More** - Loading indicator for pagination
- **Refresh** - Manual refresh capability

## Usage

The dashboard automatically loads user submissions when the "My Submissions" tab is active. Users can:

1. **View Submissions** - See all their uploaded content
2. **Load More** - Click to load additional submissions
3. **Refresh** - Manually refresh the list
4. **Upload New** - Navigate to upload page
5. **View Details** - Click on cards to see more info (to be implemented)

## Authentication

The API requires Firebase Authentication token in the Authorization header:
```
Authorization: Bearer <firebase-id-token>
```

This is automatically handled by the axios interceptor in the existing setup.

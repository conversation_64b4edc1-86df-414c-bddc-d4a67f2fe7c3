export interface FirebaseTimestamp {
  _seconds: number
  _nanoseconds: number
}

export interface SubmissionContent {
  id: string
  contentType: 'image' | 'video'
  contentUrl: string
  thumbnailUrl: string
  isMain: boolean
  uploadedAt: FirebaseTimestamp
}

export interface Submission {
  id: string
  creatorId: string
  staticContentId: string
  title: string
  visibility: 'public' | 'circle-only'
  version: number
  contents: SubmissionContent[]
  createdAt: FirebaseTimestamp
}

export interface SubmissionsResponse {
  posts: Submission[]
  count: number
  nextCursor: FirebaseTimestamp | null
  hasMore: boolean
}

export interface SubmissionsRequest {
  limit: number
  cursor?: {
    lastCreatedAt: FirebaseTimestamp
  }
}

'use client'
import React, { useState } from 'react'
import { Button } from '../ui/button'
import Image from 'next/image'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { LogOut, Settings, User, Menu, X } from 'lucide-react'
import { useModal } from '@/contexts/ModalContext'

function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const { userData, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const { setCreatorModalOpen } = useModal()
  const isHomePage = pathname === '/'

  const handleLogout = () => {
    logout()
  }

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setMobileMenuOpen(false)
  }

  const renderNavigationButtons = () => {
    if (!isHomePage || userData) return null

    return (
      <>
        <Button
          variant="ghost"
          onClick={() => {
            scrollToSection('how-it-works')
          }}
          className="text-gray-700 hover:text-primary"
        >
          How It Works
        </Button>
        <Button
          variant="ghost"
          onClick={() => scrollToSection('pricing')}
          className="text-gray-700 hover:text-primary"
        >
          Pricing
        </Button>
        <Button
          variant="ghost"
          onClick={() => setCreatorModalOpen(true)}
          className="text-gray-700 hover:text-primary"
        >
          For Influencers
        </Button>
      </>
    )
  }

  const renderMobileMenu = () => {
    if (!mobileMenuOpen || !isHomePage) return null

    return (
      <div className="md:hidden absolute top-full left-0 right-0 bg-white border-b shadow-lg z-10">
        <div className="px-2 pt-2 pb-3 space-y-1">
          {!userData && (
            <>
              <Button
                variant="ghost"
                onClick={() => {
                  scrollToSection('how-it-works')
                  setMobileMenuOpen(!mobileMenuOpen)
                }}
                className="w-full text-left justify-start text-gray-700 hover:text-primary"
              >
                How It Works
              </Button>
              <Button
                variant="ghost"
                onClick={() => {
                  scrollToSection('pricing')
                  setMobileMenuOpen(!mobileMenuOpen)
                }}
                className="w-full text-left justify-start text-gray-700 hover:text-primary"
              >
                Pricing
              </Button>
              <Button
                variant="ghost"
                onClick={() => {
                  setCreatorModalOpen(true)
                  setMobileMenuOpen(!mobileMenuOpen)
                }}
                className="w-full text-left justify-start text-gray-700 hover:text-primary"
              >
                For Influencers
              </Button>
              <Link href="/login" className="block">
                <Button
                  variant="ghost"
                  className="w-full text-left justify-start"
                >
                  Login
                </Button>
              </Link>
            </>
          )}
        </div>
      </div>
    )
  }

  return (
    <nav className="bg-white shadow-sm border-b relative z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/">
              <Image
                src="/images/logo.jpeg"
                alt="critiqle logo"
                width={140}
                height={70}
                style={{ height: '70px', objectFit: 'contain' }}
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4">
            {renderNavigationButtons()}
            {userData ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full p-0"
                  >
                    <Image
                      src={userData.profilePicture || '/images/placeholder.jpg'}
                      alt={userData.fullName || 'icon'}
                      width={32}
                      height={32}
                      className="rounded-full"
                      style={{ objectFit: 'contain' }}
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {userData.fullName}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {userData.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => router.push('/profile')}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link href="/login">
                <Button className="px-2 xs:px-4 text-[10px] xs:text-base">
                  Login
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile menu button - only show on home page */}
          {isHomePage && (
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          )}

          {/* Mobile navigation for non-home pages */}
          {!isHomePage && (
            <div className="md:hidden">
              {userData ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative h-8 w-8 rounded-full p-0"
                    >
                      <Image
                        src={
                          userData.profilePicture || '/images/placeholder.jpg'
                        }
                        alt={userData.fullName || 'icon'}
                        width={32}
                        height={32}
                        className="rounded-full"
                        style={{ objectFit: 'contain' }}
                      />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {userData.fullName}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {userData.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => router.push('/profile')}>
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <Settings className="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link href="/login">
                  <Button
                    variant="ghost"
                    className="px-2 xs:px-4 text-[10px] xs:text-base"
                  >
                    Login
                  </Button>
                </Link>
              )}
            </div>
          )}
        </div>
      </div>
      {renderMobileMenu()}
    </nav>
  )
}

export default Navbar

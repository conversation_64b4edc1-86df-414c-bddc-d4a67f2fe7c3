import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { useCategories } from '@/contexts/CategoryContext'
import { toast, type ToastT } from 'sonner'
import {
  X,
  Plus,
  Loader2,
  Instagram,
  Youtube,
  Twitter,
  Facebook,
  Linkedin,
} from 'lucide-react'
import { TikTokIcon } from '@/components/ui/icons'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { USER_ROLES } from '@/data/constant'

const socialPlatforms = [
  { name: 'Instagram', icon: Instagram, color: '#E4405F' },
  { name: 'Tik<PERSON>ok', icon: TikTokIcon, color: '#000000' },
  { name: 'YouTube', icon: Youtube, color: '#FF0000' },
  { name: 'Twitter', icon: Twitter, color: '#1DA1F2' },
  { name: 'Facebook', icon: Facebook, color: '#1877F2' },
  { name: 'LinkedIn', icon: Linkedin, color: '#0A66C2' },
]

interface CritiquerSignupModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const CritiquerSignupModal = ({
  open,
  onOpenChange,
}: CritiquerSignupModalProps) => {
  const router = useRouter()
  const { signUpWithEmailAndPassword, signUpWithGoogle, saveUserData } =
    useAuth()
  const {
    categories,
    loading: categoriesLoading,
    error: categoriesError,
  } = useCategories()
  const [loading, setLoading] = useState(false)
  const [isGoogleSignIn, setIsGoogleSignIn] = useState(false)
  const [googleUser, setGoogleUser] = useState<any>(null)
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    password: '',
    confirmPassword: '',
    socialHandles: [{ platform: '', username: '' }],
    selectedCategories: [] as string[],
  })
  const [errors, setErrors] = useState({
    socialHandles: '',
    categories: '',
  })
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  const addSocialHandle = () => {
    setFormData(prev => ({
      ...prev,
      socialHandles: [...prev.socialHandles, { platform: '', username: '' }],
    }))
  }

  const removeSocialHandle = (index: number) => {
    setFormData(prev => ({
      ...prev,
      socialHandles: prev.socialHandles.filter((_, i) => i !== index),
    }))
  }

  const updateSocialHandle = (
    index: number,
    field: 'platform' | 'username',
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      socialHandles: prev.socialHandles.map((handle, i) =>
        i === index ? { ...handle, [field]: value } : handle
      ),
    }))
  }

  const toggleCategory = (categoryId: string) => {
    setFormData(prev => {
      const categories = prev.selectedCategories
      if (categories.includes(categoryId)) {
        return {
          ...prev,
          selectedCategories: categories.filter(cat => cat !== categoryId),
        }
      } else if (categories.length < 2) {
        return {
          ...prev,
          selectedCategories: [...categories, categoryId],
        }
      }
      return prev
    })
  }

  const validateForm = () => {
    let isValid = true
    const newErrors = {
      socialHandles: '',
      categories: '',
    }

    // Validate social handles
    const hasEmptyHandles = formData.socialHandles.some(
      handle => !handle.username.trim()
    )
    if (hasEmptyHandles) {
      newErrors.socialHandles = 'Please fill in all social media handles'
      isValid = false
    }

    // Validate categories
    if (formData.selectedCategories.length === 0) {
      newErrors.categories = 'Please select at least one category'
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  const isFormValid = () => {
    // Check if all social handles have usernames
    const hasValidHandles = formData.socialHandles.every(
      handle => handle.username.trim() !== ''
    )

    // Check if at least one category is selected
    const hasValidCategories = formData.selectedCategories.length > 0

    // Check if full name is provided
    const hasValidName = formData.fullName.trim() !== ''

    // For email/password signUpWithEmailAndPassword, check if all fields are filled
    if (!isGoogleSignIn) {
      const hasValidEmail = formData.email.trim() !== ''
      const hasValidPassword = formData.password.length >= 6
      const hasValidConfirmPassword =
        formData.password === formData.confirmPassword

      return (
        hasValidHandles &&
        hasValidCategories &&
        hasValidEmail &&
        hasValidPassword &&
        hasValidConfirmPassword &&
        hasValidName
      )
    }

    return hasValidHandles && hasValidCategories && hasValidName
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return

    if (!isGoogleSignIn && formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match', {
        icon: <div className="text-red-500">✕</div>,
      })
      return
    }

    setLoading(true)
    try {
      const socialLinks = formData.socialHandles.map(
        handle => `${handle.platform}:@${handle.username}`
      )

      if (isGoogleSignIn) {
        await saveUserData({
          uid: googleUser.uid,
          email: formData.email,
          fullName: formData.fullName,
          role: USER_ROLES.critiquer,
          socialLinks,
          profilePicture: googleUser.photoURL!,
          critiquerCategory: formData.selectedCategories,
        })
        toast.success('Successfully signed up with Google.', {
          icon: <div className="text-green-500">✓</div>,
          duration: 3000,
        } as ToastT)
        onOpenChange(false)
        router.push('/')
      } else {
        await signUpWithEmailAndPassword(formData.email, formData.password, {
          email: formData.email,
          fullName: formData.fullName,
          role: USER_ROLES.critiquer,
          socialLinks,
          critiquerCategory: formData.selectedCategories,
        })
        toast.success('Please check your verification email', {
          icon: <div className="text-green-500">✓</div>,
          duration: 3000,
        } as ToastT)
        onOpenChange(false)
        router.push('/login')
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign up', {
        icon: <div className="text-red-500">✕</div>,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true)
    try {
      const user = await signUpWithGoogle()
      setGoogleUser(user)
      setIsGoogleSignIn(true)
      // Auto-fill form data from Google user
      setFormData(prev => ({
        ...prev,
        fullName: user.displayName || '',
        email: user.email || '',
      }))
      // toast.success('Google sign-in successful! Please complete your profile.');
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign up with Google', {
        icon: <div className="text-red-500">✕</div>,
      })
    } finally {
      setIsGoogleLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] p-0">
        <div className="p-3 px-6">
          <DialogHeader>
            <DialogTitle className="text-center">
              Sign up as a Critiquer
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4 pt-4">
            {!isGoogleSignIn && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleGoogleSignUp}
                  disabled={isGoogleLoading}
                >
                  {isGoogleLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                      <span>Signing in with Google...</span>
                    </div>
                  ) : (
                    <>
                      <svg className="w-5 h-5" viewBox="0 0 24 24">
                        <path
                          fill="#4285f4"
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                        />
                        <path
                          fill="#34a853"
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                        />
                        <path
                          fill="#fbbc05"
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                        />
                        <path
                          fill="#ea4335"
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                        />
                      </svg>
                      Continue with Google
                    </>
                  )}
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">
                      Or continue with email
                    </span>
                  </div>
                </div>
              </>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="fullName">Full Name *</Label>
                <Input
                  id="fullName"
                  type="text"
                  value={formData.fullName}
                  onChange={e =>
                    setFormData(prev => ({ ...prev, fullName: e.target.value }))
                  }
                  required
                  className="mt-1"
                  disabled={loading || isGoogleLoading}
                  placeholder="Enter your full name"
                />
              </div>

              {!isGoogleSignIn ? (
                <>
                  <div>
                    <Label htmlFor="email">Email address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          email: e.target.value,
                        }))
                      }
                      required
                      className="mt-1"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          password: e.target.value,
                        }))
                      }
                      required
                      className="mt-1"
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <Label htmlFor="confirmPassword">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={e =>
                        setFormData(prev => ({
                          ...prev,
                          confirmPassword: e.target.value,
                        }))
                      }
                      required
                      className="mt-1"
                      disabled={loading}
                    />
                  </div>
                </>
              ) : (
                <div>
                  <Label htmlFor="email">Email address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    disabled={true}
                    className="mt-1 bg-gray-50"
                  />
                </div>
              )}

              <div>
                <Label>Social Media Handles *</Label>
                <p className="text-sm text-gray-600 mb-4">
                  Add your social media accounts for verification
                </p>

                <div className="space-y-5">
                  {formData.socialHandles.map((handle, index) => (
                    <div key={index} className="flex gap-1 items-start">
                      <Select
                        value={handle.platform}
                        onValueChange={value =>
                          updateSocialHandle(index, 'platform', value)
                        }
                        disabled={loading || isGoogleLoading}
                      >
                        <SelectTrigger className="w-36 sm:w-40 h-10">
                          <SelectValue
                            placeholder="Select Platform"
                            className="text-left"
                          >
                            {handle.platform &&
                              (() => {
                                const selectedPlatform = socialPlatforms.find(
                                  p => p.name === handle.platform
                                )
                                const IconComponent = selectedPlatform?.icon
                                return (
                                  <div className="flex items-center gap-2">
                                    {IconComponent && (
                                      <IconComponent
                                        className="h-4 w-4"
                                        style={{
                                          color: selectedPlatform?.color,
                                        }}
                                      />
                                    )}
                                    <span className="text-sm truncate">
                                      {handle.platform}
                                    </span>
                                  </div>
                                )
                              })()}
                          </SelectValue>
                        </SelectTrigger>
                        <SelectContent className="max-h-44 z-[10000] overflow-y-auto">
                          {socialPlatforms.map(platform => {
                            const IconComponent = platform.icon
                            const isSelected = handle.platform === platform.name
                            return (
                              <SelectItem
                                key={platform.name}
                                value={platform.name}
                                className={`w-full cursor-pointer transition-colors duration-200 ${
                                  isSelected
                                    ? 'bg-primary/10 border-l-2 border-primary text-primary font-medium'
                                    : 'hover:bg-gray-50 focus:bg-gray-50'
                                }`}
                              >
                                <div className="flex flex-row items-center justify-start gap-3 py-2 px-1">
                                  {IconComponent && (
                                    <IconComponent
                                      className="h-4 w-4 flex-shrink-0"
                                      style={{ color: platform.color }}
                                    />
                                  )}
                                  <div
                                    className={`flex-1 text-sm ${isSelected ? 'font-medium' : ''}`}
                                  >
                                    {platform.name}
                                  </div>
                                </div>
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>

                      <div className="flex flex-1">
                        <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-md">
                          @
                        </span>
                        <Input
                          value={handle.username}
                          onChange={e =>
                            updateSocialHandle(
                              index,
                              'username',
                              e.target.value
                            )
                          }
                          className="rounded-l-none h-10"
                          placeholder="username"
                          disabled={
                            loading || isGoogleLoading || !handle.platform
                          }
                          autoCapitalize="none"
                          autoCorrect="off"
                          spellCheck="false"
                        />
                      </div>

                      {formData.socialHandles.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={() => removeSocialHandle(index)}
                          className="h-10 w-10 border-gray-200 hover:border-red-300 hover:bg-red-50"
                          disabled={loading || isGoogleLoading}
                        >
                          <X className="h-4 w-4 text-gray-500" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={addSocialHandle}
                  className="w-full mt-6 h-10"
                  disabled={loading || isGoogleLoading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Another Social Media Account
                </Button>
                {errors.socialHandles && (
                  <p className="text-sm text-red-500 mt-2">
                    {errors.socialHandles}
                  </p>
                )}
              </div>

              <div>
                <Label>Expertise Categories * (Select 1-2)</Label>
                <p className="text-sm text-gray-600 mb-4">
                  Choose your areas of expertise
                </p>
                {categoriesLoading ? (
                  <div className="flex items-center justify-center p-6">
                    <Loader2 className="h-6 w-6 animate-spin text-primary" />
                  </div>
                ) : categoriesError ? (
                  <p className="text-sm text-red-500">{categoriesError}</p>
                ) : (
                  <div className="space-y-3">
                    <div className="flex flex-wrap gap-2">
                      {categories.map(category => (
                        <Badge
                          key={category.id}
                          variant={
                            formData.selectedCategories.includes(category.id)
                              ? 'default'
                              : 'outline'
                          }
                          className={`cursor-pointer transition-all duration-200 ${
                            formData.selectedCategories.includes(category.id)
                              ? 'bg-primary hover:bg-primary/90 text-primary-foreground'
                              : 'hover:bg-gray-100 border-gray-300'
                          } ${loading || isGoogleLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() =>
                            !loading &&
                            !isGoogleLoading &&
                            toggleCategory(category.id)
                          }
                        >
                          {category.categoryName}
                        </Badge>
                      ))}
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">
                        Selected:{' '}
                        <span className="font-medium">
                          {formData.selectedCategories.length}
                        </span>
                        /2
                      </span>
                      {formData.selectedCategories.length === 2 && (
                        <span className="text-primary text-xs">
                          Maximum 2 categories allowed
                        </span>
                      )}
                    </div>
                  </div>
                )}
                {errors.categories && (
                  <p className="text-sm text-red-500 mt-2">
                    {errors.categories}
                  </p>
                )}
              </div>

              <div className="bg-primary/10 p-3 rounded-lg">
                <p className="text-sm">
                  <strong>Note:</strong> Your social media accounts will be
                  verified by our team. You'll be notified once your application
                  is approved.
                </p>
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={loading || isGoogleLoading || !isFormValid()}
                >
                  {loading ? 'Submitting...' : 'Submit Application'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default CritiquerSignupModal

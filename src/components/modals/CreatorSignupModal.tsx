import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '../ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { toast, type ToastT } from 'sonner'
import { USER_ROLES } from '@/data/constant'
import { ScrollArea } from '../ui/scroll-area'

interface CreatorSignupModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const CreatorSignupModal = ({
  open,
  onOpenChange,
}: CreatorSignupModalProps) => {
  const router = useRouter()
  const { signUpWithEmailAndPassword, signUpWithGoogle, saveUserData } =
    useAuth()
  const [loading, setLoading] = useState(false)
  const [isGoogleSignIn, setIsGoogleSignIn] = useState(false)
  const [googleUser, setGoogleUser] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    business: '',
    brand: '',
    phone: '',
    password: '',
    confirmPassword: '',
  })

  const isFormValid = () => {
    if (isGoogleSignIn) {
      return (
        formData.name.trim() !== '' &&
        formData.business.trim() !== '' &&
        formData.brand.trim() !== ''
      )
    }
    return (
      formData.name.trim() !== '' &&
      formData.email.trim() !== '' &&
      formData.password.trim() !== '' &&
      formData.confirmPassword.trim() !== '' &&
      formData.password === formData.confirmPassword &&
      formData.password.length >= 6
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!isFormValid()) {
      toast.error('Please fill in all required fields correctly', {
        icon: <div className="text-red-500">✕</div>,
      })
      return
    }

    setLoading(true)
    try {
      if (isGoogleSignIn) {
        await saveUserData({
          uid: googleUser.uid,
          email: formData.email,
          fullName: formData.name,
          businessName: formData.business,
          brandName: formData.brand,
          phoneNumber: formData.phone,
          role: USER_ROLES.creator,
          profilePicture: googleUser.photoURL!,
        })
        toast.success('Successfully signed up with Google.', {
          icon: <div className="text-green-500">✓</div>,
          duration: 3000,
        } as ToastT)
        onOpenChange(false)
        router.push('/')
      } else {
        await signUpWithEmailAndPassword(formData.email, formData.password, {
          fullName: formData.name,
          businessName: formData.business,
          brandName: formData.brand,
          phoneNumber: formData.phone,
          email: formData.email,
          role: USER_ROLES.creator,
        })

        toast.success('Please check your verification email', {
          icon: <div className="text-green-500">✓</div>,
          duration: 3000,
        } as ToastT)
        onOpenChange(false)
        router.push('/login')
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign up', {
        icon: <div className="text-red-500">✕</div>,
        duration: 4500,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignup = async () => {
    setLoading(true)
    try {
      const user = await signUpWithGoogle()
      setGoogleUser(user)
      setIsGoogleSignIn(true)
      // Auto-fill form data from Google user
      setFormData(prev => ({
        ...prev,
        name: user.displayName || '',
        email: user.email || '',
      }))
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign up with Google', {
        icon: <div className="text-red-500">✕</div>,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md max-h-[90vh] p-0">
        <div className="p-3 px-6">
          <DialogHeader>
            <DialogTitle className="text-center">
              Join CRITIQLE as a Creator
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 pt-4">
            {!isGoogleSignIn && (
              <>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleGoogleSignup}
                  disabled={loading}
                >
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="#4285f4"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="#34a853"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="#fbbc05"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="#ea4335"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  Continue with Google
                </Button>

                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator className="w-full" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or continue with email
                    </span>
                  </div>
                </div>
              </>
            )}

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={e => handleInputChange('name', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              {!isGoogleSignIn ? (
                <>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={e => handleInputChange('email', e.target.value)}
                      required
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <Label htmlFor="password">Password *</Label>
                    <Input
                      id="password"
                      type="password"
                      value={formData.password}
                      onChange={e =>
                        handleInputChange('password', e.target.value)
                      }
                      required
                      disabled={loading}
                      minLength={6}
                    />
                  </div>

                  <div>
                    <Label htmlFor="confirmPassword">Confirm Password *</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={formData.confirmPassword}
                      onChange={e =>
                        handleInputChange('confirmPassword', e.target.value)
                      }
                      required
                      disabled={loading}
                      minLength={6}
                    />
                  </div>
                </>
              ) : (
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    disabled={true}
                    className="bg-gray-50"
                  />
                </div>
              )}

              <div>
                <Label htmlFor="business">Business/Company *</Label>
                <Input
                  id="business"
                  value={formData.business}
                  onChange={e => handleInputChange('business', e.target.value)}
                  disabled={loading}
                  required={isGoogleSignIn}
                />
              </div>

              <div>
                <Label htmlFor="brand">Brand Name *</Label>
                <Input
                  id="brand"
                  value={formData.brand}
                  onChange={e => handleInputChange('brand', e.target.value)}
                  disabled={loading}
                  required={isGoogleSignIn}
                />
              </div>

              <div>
                <Label htmlFor="phone">Phone (Optional)</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={e => handleInputChange('phone', e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  className="flex-1"
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={loading || !isFormValid()}
                >
                  {loading
                    ? 'Creating Account...'
                    : isGoogleSignIn
                      ? 'Complete Sign Up'
                      : 'Create Account'}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default CreatorSignupModal

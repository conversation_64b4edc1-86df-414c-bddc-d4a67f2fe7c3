'use client'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'

function Footer() {
  const pathname = usePathname()
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">CRITIQLE</h3>
            <p className="text-gray-400">
              Content critiques by verified niche voices
            </p>
          </div>
          <div>
            <h4 className="font-semibold mb-3">Support</h4>
            <ul className="space-y-2 text-gray-400">
              <li>
                <Link
                  href="/terms"
                  className={`hover:text-white ${pathname === '/terms' ? 'font-bold text-white' : ''}`}
                >
                  Terms
                </Link>
              </li>
              <li>
                <Link
                  href="/privacy"
                  className={`hover:text-white ${pathname === '/privacy' ? 'font-bold text-white' : ''}`}
                >
                  Privacy
                </Link>
              </li>
              <li>
                <Link
                  href="/contact"
                  className={`hover:text-white ${pathname === '/contact' ? 'font-bold text-white' : ''}`}
                >
                  Contact
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2025 CRITIQLE. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}
export default Footer

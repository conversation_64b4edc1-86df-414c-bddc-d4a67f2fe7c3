'use client'

import { useState } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'

interface CaptionInputProps {
  value: string
  onChange: (value: string) => void
  maxLength?: number
  placeholder?: string
  className?: string
}

export const CaptionInput = ({
  value,
  onChange,
  maxLength = 450,
  placeholder = 'Write a short caption to guide feedback...',
  className = '',
}: CaptionInputProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    if (newValue.length <= maxLength) {
      onChange(newValue)
    }
  }

  return (
    <div className={`mb-8 ${className}`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-semibold text-app-text-primary text-[18px] leading-[22px]">
          Add a Caption
        </h3>
        <Badge
          variant="secondary"
          className="text-xs font-normal bg-app-green-light"
        >
          {value.length}/{maxLength}
        </Badge>
      </div>
      <p className="text-app-text-primary mb-2 text-[12px] leading-[18px]">
        What would you like feedback on?
      </p>
      <Textarea
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        className="min-h-[100px] resize-none font-poppins border-gray-200 focus:border-green-500 focus:ring-green-500 bg-white"
      />
    </div>
  )
}

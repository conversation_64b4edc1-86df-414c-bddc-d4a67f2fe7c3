'use client'

import { useEffect, useState, useRef } from 'react'
import { ChevronDown } from 'lucide-react'
import { useCategories } from '@/hooks/useCategories'

interface NicheSelectorProps {
  selectedNiche: string
  onNicheChange: (niche: string) => void
  required?: boolean
  className?: string
}

export const NicheSelector = ({
  selectedNiche,
  onNicheChange,
  required = true,
  className = '',
}: NicheSelectorProps) => {
  const { categories, loading } = useCategories()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Auto-select first category when categories load and no selection exists
  useEffect(() => {
    if (!loading && categories.length > 0 && !selectedNiche) {
      onNicheChange(categories[0].id)
    }
  }, [categories, loading, selectedNiche, onNicheChange])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleValueChange = (value: string) => {
    onNicheChange(value)
    setIsOpen(false)
  }

  const selectedCategory = categories.find(cat => cat.id === selectedNiche)
  const displayText = loading
    ? 'Loading categories...'
    : selectedCategory
      ? selectedCategory.categoryName
      : 'Select your Niche'

  return (
    <div className={`mb-8 ${className}`}>
      <h3 className="font-semibold text-app-text-primary mb-2 text-[18px] leading-[22px]">
        Select your Niche{required && '*'}
      </h3>

      <div className="relative" ref={dropdownRef}>
        {/* Custom Select Trigger */}
        <button
          type="button"
          onClick={() => !loading && setIsOpen(!isOpen)}
          disabled={loading}
          className="w-full h-12 bg-white border border-app-gray-border rounded-md px-4 pr-10 font-poppins text-app-text-secondary focus:border-app-green focus:ring-1 focus:ring-app-green focus:outline-none disabled:opacity-50 cursor-pointer text-left flex items-center justify-between"
        >
          <span
            className={
              selectedCategory
                ? 'text-app-text-primary'
                : 'text-app-text-secondary'
            }
          >
            {displayText}
          </span>
          <ChevronDown
            className={`h-4 w-4 text-app-text-secondary transition-transform ${isOpen ? 'rotate-180' : ''}`}
          />
        </button>

        {/* Custom Dropdown */}
        {isOpen && !loading && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-app-gray-border rounded-md shadow-lg max-h-60 overflow-auto">
            {categories.map(category => (
              <button
                key={category.id}
                type="button"
                onClick={() => handleValueChange(category.id)}
                style={{ borderBottom: '2px solid #00000017' }}
                className={`w-full px-4 py-3 text-left font-poppins text-sm  hover:bg-app-green-light hover:text-app-green focus:bg-app-green-light focus:text-app-green focus:outline-none transition-colors ${
                  selectedNiche === category.id
                    ? 'bg-app-green-light text-app-green font-semibold'
                    : 'text-gray-700'
                }`}
              >
                {category.categoryName}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

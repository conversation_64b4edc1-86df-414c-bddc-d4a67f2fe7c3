'use client'

import { Button } from '@/components/ui/button'
import { useCallback, useEffect, useRef, useState } from 'react'

import { Upload, X } from 'lucide-react'
import { toast } from 'sonner'

interface MediaFile {
  file: File
  preview: string
  type: 'image' | 'video'
}

interface MediaUploadProps {
  files: MediaFile[]
  onFilesChange: (files: MediaFile[]) => void
  maxFiles?: number
  maxFileSize?: number // in MB
  maxVideoDuration?: number // in seconds
  acceptedTypes?: string[]
  className?: string
}

export const MediaUpload = ({
  files,
  onFilesChange,
  maxFiles = 4,
  maxFileSize = 300,
  maxVideoDuration = 15,
  acceptedTypes = ['image/jpeg', 'image/png', 'video/mp4'],
  className = '',
}: MediaUploadProps) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const [mounted, setMounted] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Ensure component is mounted before creating object URLs
  useEffect(() => {
    setMounted(true)
  }, [])

  // Update existing files with preview URLs after mount
  useEffect(() => {
    if (mounted && files.length > 0) {
      const updatedFiles = files.map(file => {
        if (!file.preview && file.file && typeof window !== 'undefined') {
          return {
            ...file,
            preview: URL.createObjectURL(file.file),
          }
        }
        return file
      })

      // Only update if there are changes
      const hasChanges = updatedFiles.some(
        (file, index) => file.preview !== files[index].preview
      )
      if (hasChanges) {
        onFilesChange(updatedFiles)
      }
    }
  }, [mounted, files, onFilesChange])

  // Cleanup object URLs when component unmounts
  useEffect(() => {
    return () => {
      files.forEach(file => {
        if (file.preview && file.preview.startsWith('blob:')) {
          URL.revokeObjectURL(file.preview)
        }
      })
    }
  }, [])

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported. Please use JPG, PNG, or MP4.`
    }

    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB.`
    }

    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')
    const hasImages = files.some(f => f.type === 'image')
    const hasVideos = files.some(f => f.type === 'video')

    // If trying to add video but images already exist
    if (isVideo && hasImages) {
      return 'Cannot mix images and videos. Please remove images first to add a video.'
    }

    // If trying to add image but video already exists
    if (isImage && hasVideos) {
      return 'Cannot mix images and videos. Please remove the video first to add images.'
    }

    // If video and already have a video
    if (isVideo && hasVideos) {
      return 'Only one video file is allowed.'
    }

    // If image and already have 4 images
    if (isImage && hasImages && files.length >= 4) {
      return 'Maximum 4 images allowed.'
    }

    return null
  }

  const validateVideoDuration = (file: File): Promise<string | null> => {
    return new Promise(resolve => {
      if (!file.type.startsWith('video/') || typeof window === 'undefined') {
        resolve(null)
        return
      }

      const video = document.createElement('video')
      video.preload = 'metadata'

      video.onloadedmetadata = () => {
        URL.revokeObjectURL(video.src)
        if (video.duration > maxVideoDuration) {
          resolve(
            `Video duration must be ${maxVideoDuration} seconds or less. Current duration: ${Math.round(video.duration)}s`
          )
        } else {
          resolve(null)
        }
      }

      video.onerror = () => {
        URL.revokeObjectURL(video.src)
        resolve(
          'Unable to validate video duration. Please try a different file.'
        )
      }

      video.src = URL.createObjectURL(file)
    })
  }

  const processFiles = useCallback(
    async (fileList: FileList) => {
      const newFiles: MediaFile[] = []

      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i]
        const validationError = validateFile(file)

        if (validationError) {
          toast.error('Upload Error', {
            description: validationError,
          })
          return
        }

        // Check video duration for video files
        const durationError = await validateVideoDuration(file)
        if (durationError) {
          toast.error('Upload Error', {
            description: durationError,
          })
          return
        }

        const preview =
          typeof window !== 'undefined' ? URL.createObjectURL(file) : ''
        const type = file.type.startsWith('image/') ? 'image' : 'video'

        newFiles.push({ file, preview, type })
      }

      onFilesChange([...files, ...newFiles])
    },
    [
      files,
      onFilesChange,
      maxFiles,
      maxFileSize,
      maxVideoDuration,
      acceptedTypes,
    ]
  )

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsDragOver(false)

      const droppedFiles = e.dataTransfer.files
      if (droppedFiles.length > 0) {
        processFiles(droppedFiles)
      }
    },
    [processFiles]
  )

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleFileSelect = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files
      if (selectedFiles && selectedFiles.length > 0) {
        processFiles(selectedFiles)
      }

      // Clear the input value so the same file can be selected again
      if (e.target) {
        e.target.value = ''
      }
    },
    [processFiles]
  )

  const handleBrowseClick = () => {
    // Clear the input value before opening file dialog
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
      fileInputRef.current.click()
    }
  }

  const removeFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index)
    onFilesChange(updatedFiles)

    // Revoke object URL to prevent memory leaks
    URL.revokeObjectURL(files[index].preview)
  }

  // Helper functions to determine current state
  const hasImages = files.some(f => f.type === 'image')
  const hasVideos = files.some(f => f.type === 'video')
  const canAddMore = hasVideos ? false : hasImages ? files.length < 4 : true

  // Dynamic description based on current selection
  const getDescription = () => {
    if (hasVideos) {
      return 'Video selected. Only one video file is allowed.'
    }
    if (hasImages) {
      return `${files.length}/4 images selected. You can add ${4 - files.length} more images.`
    }
    return 'Add up to 4 images or 1 video (JPG, PNG, MP4)'
  }

  return (
    <div className={`mb-8 ${className}`}>
      <div className="flex items-center gap-2 mb-2">
        <h3 className="font-semibold text-app-text-primary text-[18px] leading-[22px]">
          Upload Media
        </h3>
      </div>
      <p className="mb-2 text-[12px] leading-[18px] font-medium text-app-text-primary">
        {getDescription()}
      </p>

      <div
        className={`border-2 border-dashed rounded-lg transition-colors ${
          files.length > 0 ? 'p-4' : 'p-6'
        } ${
          !canAddMore
            ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
            : isDragOver
              ? 'border-green-400 bg-green-50'
              : 'border-gray-300 hover:border-green-400 bg-white cursor-pointer'
        }`}
        onDrop={canAddMore ? handleDrop : undefined}
        onDragOver={canAddMore ? handleDragOver : undefined}
        onDragLeave={canAddMore ? handleDragLeave : undefined}
      >
        {files.length > 0 ? (
          // Show preview in drag area
          <div className="space-y-4">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {files.map((mediaFile, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                    {mediaFile.preview ? (
                      mediaFile.type === 'image' ? (
                        <img
                          src={mediaFile.preview}
                          alt={`Selected ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <video
                          src={mediaFile.preview}
                          className="w-full h-full object-cover"
                          muted
                        />
                      )
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        <Upload className="h-8 w-8" />
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => removeFile(index)}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>

            <div className="text-center pt-4 border-t border-gray-200">
              <div className="flex justify-center">
                {canAddMore && (
                  <Button
                    variant="outline"
                    className="font-poppins px-6 py-2 text-sm border-green-500 text-green-600 hover:bg-green-50 w-full xs:w-auto"
                    onClick={handleBrowseClick}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {hasImages
                      ? `Add More (${4 - files.length} left)`
                      : 'Add More'}
                  </Button>
                )}
              </div>
            </div>
          </div>
        ) : (
          // Show empty state
          <div className="text-center">
            <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
            <p className="text-base text-[#343434] mb-2 font-medium">
              Choose a file or drag & drop it here
            </p>
            <p className="text-sm text-gray-500 mb-6">
              JPG, PNG, MP4 (max {maxVideoDuration}s for videos)
            </p>
            <Button
              variant="outline"
              className="font-poppins px-8 py-2 border-green-500 text-green-600 hover:bg-green-50"
              onClick={handleBrowseClick}
            >
              Browse File
            </Button>
          </div>
        )}

        <input
          ref={fileInputRef}
          type="file"
          multiple={hasVideos ? false : true}
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={!canAddMore}
        />
      </div>
    </div>
  )
}

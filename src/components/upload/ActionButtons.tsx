'use client'

import { But<PERSON> } from '@/components/ui/button'

interface ActionButtonsProps {
  onSaveDraft: () => void
  onSubmit: () => void
  isSubmitting?: boolean
  isDraftSaving?: boolean
  isFormValid?: boolean
  className?: string
}

export const ActionButtons = ({
  onSaveDraft,
  onSubmit,
  isSubmitting = false,
  isDraftSaving = false,
  isFormValid = true,
  className = '',
}: ActionButtonsProps) => {
  return (
    <div className={`flex gap-4 pt-4 ${className}`}>
      <Button
        onClick={onSaveDraft}
        disabled={isDraftSaving || isSubmitting}
        className="flex-1 bg-app-green hover:bg-green-700 h-14 font-poppins font-semibold text-base"
      >
        {isDraftSaving ? 'Saving...' : 'Save Draft'}
      </Button>
      <Button
        onClick={onSubmit}
        // disabled={!isFormValid || isSubmitting || isDraftSaving}
        disabled={true}
        variant="outline"
        className="flex-1 h-14 font-poppins font-semibold text-base border-2 border-gray-300 hover:bg-gray-50 bg-app-gray text-white"
      >
        {isSubmitting ? 'Submitting...' : 'Request Feedback'}
      </Button>
    </div>
  )
}

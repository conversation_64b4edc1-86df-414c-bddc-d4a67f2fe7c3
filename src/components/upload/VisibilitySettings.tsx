'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Users, Globe, Lock, Info } from 'lucide-react'

export type VisibilityType = 'circle-only' | 'community'

interface VisibilitySettingsProps {
  visibility: VisibilityType
  onVisibilityChange: (visibility: VisibilityType) => void
  enableFeedback: boolean
  onEnableFeedbackChange: (enabled: boolean) => void
  className?: string
}

export const VisibilitySettings = ({
  visibility,
  onVisibilityChange,
  enableFeedback,
  onEnableFeedbackChange,
  className = '',
}: VisibilitySettingsProps) => {
  return (
    <div className={`mb-8 ${className}`}>
      <h3 className="font-semibold text-app-text-primary mb-2 text-[18px] leading-[22px]">
        Visibility
      </h3>

      <div className="space-y-3">
        <Button
          variant="outline"
          className={`w-full h-auto p-4 justify-start text-left transition-all ${
            visibility === 'circle-only'
              ? 'bg-green-100 hover:bg-green-50 hover:text-green-700 text-green-700 border-green-200 font-semibold'
              : 'bg-white hover:bg-gray-50 text-app-text-primary border-gray-200'
          }`}
          onClick={() => onVisibilityChange('circle-only')}
        >
          <div className="flex items-start space-x-4 w-full">
            <Users
              className={`h-5 w-5 mt-0.5 flex-shrink-0 ${
                visibility === 'circle-only'
                  ? 'text-green-600'
                  : 'text-gray-500'
              }`}
            />
            <div className="flex-1">
              <div className="font-semibold text-base">Circle Only</div>
              <p className="text-sm text-[#343434] mt-2 leading-relaxed font-normal text-wrap">
                Only people in your Circle can view and vote. Members will be
                notified when you post.
              </p>
            </div>
          </div>
        </Button>

        <Button
          variant="outline"
          className={`w-full h-auto p-4 justify-start text-left transition-all ${
            visibility === 'community'
              ? 'bg-green-100 hover:bg-green-50 hover:text-green-700 text-green-700 border-green-200 font-semibold'
              : 'bg-white hover:bg-gray-50 text-app-text-primary border-gray-200'
          }`}
          onClick={() => onVisibilityChange('community')}
        >
          <div className="flex items-start space-x-4 w-full">
            <Globe
              className={`h-5 w-5 mt-0.5 flex-shrink-0 ${
                visibility === 'community' ? 'text-green-600' : 'text-gray-500'
              }`}
            />
            <div className="flex-1">
              <div className="font-semibold text-base">Community</div>
              <p className="text-sm text-[#343434] mt-2 leading-relaxed font-normal text-wrap">
                Anyone on Critiqle can view and vote on this post. It will
                appear in the Community feed.
              </p>
            </div>
          </div>
        </Button>
      </div>

      {/* Enable Feedback Switch */}
      <div className="flex items-center justify-between p-4 mt-6 bg-white rounded-lg border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <Lock className="h-4 w-4 text-green-600" />
          </div>
          <div>
            <p className="font-semibold text-app-text-primary text-sm">
              Enable Feedback{' '}
              {visibility === 'community' ? '(if visibility = Community)' : ''}
            </p>
            <p className="text-xs text-[#343434] mt-1 text-wrap">
              Community posts allow Feedback from any registered user
            </p>
          </div>
        </div>
        <Switch
          checked={enableFeedback}
          onCheckedChange={onEnableFeedbackChange}
          className="data-[state=checked]:bg-app-green"
        />
      </div>

      {/* Compact Info Banner */}
      <div className="mt-6 p-3 rounded-lg bg-app-green-alpha">
        <div className="flex items-center gap-3">
          <div className="w-5 h-5 rounded-full flex items-center justify-center">
            <Info />
          </div>
          <div className="text-sm text-app-text-primary">
            <p className="font-regular leading-tight">
              Only people in your circle can view and vote. You can share the
              link, but recipients must be added to your circle to access.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

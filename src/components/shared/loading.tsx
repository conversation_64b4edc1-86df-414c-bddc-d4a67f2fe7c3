import { Loader } from 'lucide-react'

const Loading = () => {
  return (
    <div className="fixed inset-0 bg-background backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <Loader className="w-10 h-10 animate-spin text-primary" />
        <p className="text-gray-500">Loading...</p>
      </div>
    </div>
  )
}

export default Loading

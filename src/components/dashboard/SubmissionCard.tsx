'use client'

import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { useAuth } from '@/contexts/AuthContext'
import { Category } from '@/types/category'
import { Submission } from '@/types/submission'
import { BarChart3, Heart, Share } from 'lucide-react'
import { useState } from 'react'

interface SubmissionCardProps {
  submission: Submission
  categories: Category[]
}

export const SubmissionCard = ({
  submission,
  categories,
}: SubmissionCardProps) => {
  const { userData } = useAuth()
  const [liked, setLiked] = useState(false)

  // Get category name from ID
  const getCategoryName = (categoryId: string) => {
    // For now, we'll extract from staticContentId or use a fallback
    // The API doesn't seem to include category info directly
    return 'Personal Blog' // Default category as shown in the design
  }

  // Format timestamp to readable date
  const formatDate = (timestamp: any) => {
    if (timestamp && timestamp._seconds) {
      const date = new Date(timestamp._seconds * 1000)
      const now = new Date()
      const diffInHours = Math.floor(
        (now.getTime() - date.getTime()) / (1000 * 60 * 60)
      )

      if (diffInHours < 24) {
        return `${diffInHours} hours and ${Math.floor((now.getTime() - date.getTime()) / (1000 * 60)) % 60} mins`
      } else {
        const diffInDays = Math.floor(diffInHours / 24)
        return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`
      }
    }
    return 'Recently'
  }

  // Get main content (first content item or the one marked as main)
  const mainContent =
    submission.contents.find(content => content.isMain) ||
    submission.contents[0]

  // Get additional content items (excluding main)
  const additionalContent = submission.contents
    .filter(content => !content.isMain)
    .slice(0, 3)

  // Mock insights data (since not provided by API)
  const insights = {
    awesome: 100,
    almostPerfect: 0,
    gettingCloser: 0,
  }

  return (
    <div className="w-full max-w-sm">
      <Card className="bg-app-green-light rounded-xl overflow-hidden shadow-lg border-0">
        {/* Header with user info */}
        <div className="p-4 pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden">
                <img
                  src={userData?.profilePicture || '/default-avatar.svg'}
                  alt={userData?.fullName || 'User'}
                  className="w-full h-full object-cover"
                  onError={e => {
                    const target = e.target as HTMLImageElement
                    target.src = '/default-avatar.svg'
                  }}
                />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 text-sm">
                  {userData?.fullName || 'Sarah Niles'}
                </h3>
                <p className="text-xs text-gray-600">
                  📍 Post Ends in 21 hours and 34 mins
                </p>
              </div>
            </div>
            <Badge
              variant="secondary"
              className="bg-white text-gray-700 text-xs px-2 py-1"
            >
              {getCategoryName(submission.staticContentId)}
            </Badge>
          </div>
        </div>

        {/* Post content */}
        <div className="px-4 pb-3">
          <p className="text-gray-900 text-sm font-medium">
            {submission.title}
          </p>
        </div>

        {/* Main image/video */}
        <div className="px-4 pb-3">
          <div className="rounded-lg overflow-hidden bg-gray-100">
            {mainContent?.contentType === 'video' ? (
              <video
                src={mainContent.contentUrl}
                poster={mainContent.thumbnailUrl}
                className="w-full h-64 object-cover"
                controls={false}
              />
            ) : (
              <img
                src={mainContent?.contentUrl || mainContent?.thumbnailUrl}
                alt={submission.title}
                className="w-full h-64 object-cover"
                onError={e => {
                  const target = e.target as HTMLImageElement
                  target.src =
                    mainContent?.thumbnailUrl || '/placeholder-image.png'
                }}
              />
            )}
          </div>
        </div>

        {/* Additional media thumbnails */}
        {additionalContent.length > 0 && (
          <div className="px-4 pb-3">
            <div className="flex space-x-2">
              {additionalContent.map((content, index) => (
                <div key={content.id} className="relative">
                  <div className="w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
                    <img
                      src={content.thumbnailUrl}
                      alt={`Additional content ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {index === 2 && additionalContent.length > 3 && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs font-semibold">
                        +{additionalContent.length - 2}
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Engagement row */}
        <div className="px-4 pb-3">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setLiked(!liked)}
              className="flex items-center space-x-1"
            >
              <Heart
                className={`w-5 h-5 ${liked ? 'fill-red-500 text-red-500' : 'text-gray-600'}`}
              />
              <span className="text-sm text-gray-600">25</span>
            </button>
            <button className="flex items-center space-x-1">
              <Share className="w-5 h-5 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Insights section */}
        <div className="px-4 pb-4">
          <div className="flex items-center space-x-2 mb-3">
            <BarChart3 className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-semibold text-gray-900">
              Insights
            </span>
          </div>

          <div className="flex justify-between">
            <div className="text-center">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mb-1">
                <span className="text-white text-xs">⭐</span>
              </div>
              <div className="text-xs font-semibold text-gray-900">
                {insights.awesome}%
              </div>
              <div className="text-xs text-gray-600">Awesome</div>
            </div>

            <div className="text-center">
              <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center mb-1">
                <span className="text-white text-xs">👍</span>
              </div>
              <div className="text-xs font-semibold text-gray-900">
                {insights.almostPerfect}%
              </div>
              <div className="text-xs text-gray-600">Almost Perfect</div>
            </div>

            <div className="text-center">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mb-1">
                <span className="text-white text-xs">🎯</span>
              </div>
              <div className="text-xs font-semibold text-gray-900">
                {insights.gettingCloser}%
              </div>
              <div className="text-xs text-gray-600">Getting Closer</div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  )
}

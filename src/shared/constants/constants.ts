//Application Constants
export const AUTH_TOKEN = 'AUTH_TOKEN'
export const AUTH_TOKEN_HEADER = 'Authorization'
export const RESEND_EMAIL_TIME = 45

// API Constants
export const HEALTH_CHECK = '/'
export const GET_USER = 'getUser'
export const ADD_OR_UPDATE_USER = 'upsertUser'
export const GET_CRITIQUER_CATEGORIES = 'getContentCategories'
export const CREATE_SUBMISSION = 'createSubmission'
export const GET_USER_SUBMISSIONS = 'getSubmissionsForUserProfile'
export const ADD_ARTIFICIAL_USER = 'upsertArtificialUser'
export const CHECK_USER_IN_AUTH_BY_EMAIL = 'getAuthUserByEmail'

// ROUTES Constants
export const ROUTES = {
  dashboard: '/',
  login: '/login',
}

export const DASHBOARD = '/'

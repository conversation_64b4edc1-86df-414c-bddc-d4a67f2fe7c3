'use client'
import { useRouter } from 'next/navigation'
import React, { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import Loading from '@/components/shared/loading'

const withAuth = (Component: React.ComponentType<any>) => {
  const AuthWrapper = (props: any) => {
    const router = useRouter()
    const { userData, loading } = useAuth()

    useEffect(() => {
      if (!router) return

      if (!userData && !loading) {
        router.push('/')
      }
    }, [router, userData, loading])

    if (loading || (!userData && !loading)) {
      return <Loading />
    }

    return <Component {...props} />
  }

  return AuthWrapper
}

export default withAuth

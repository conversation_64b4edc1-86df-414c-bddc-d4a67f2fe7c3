// "use client";
// import { redirect } from "next/navigation";

// const withRole = (Component: React.ComponentType<any>, role: string) => {
//   const RoleCheck = (props: any) => {
//     const userRole =
//       useSelector(selectUser)?.roles.map((role) => role.role) || [];
//     if (userRole.includes(role)) {
//       return <Component {...props} />;
//     } else {
//       return redirect("not-found");
//     }
//   };

//   return RoleCheck;
// };

// export default withRole;

'use client'

import { useState } from 'react'
import type { UploadedMedia } from '@/lib/firebase/upload'
import { uploadMultipleMedia } from '@/lib/firebase/upload'
import { useAuth } from '@/contexts/AuthContext'
import NetworkManager from '@/lib/axios/network-manager'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { ActionButtons } from '@/components/upload/ActionButtons'
import { CaptionInput } from '@/components/upload/CaptionInput'
import { MediaUpload } from '@/components/upload/MediaUpload'
import { NicheSelector } from '@/components/upload/NicheSelector'
import {
  VisibilityType,
  VisibilitySettings,
} from '@/components/upload/VisibilitySettings'

interface MediaFile {
  file: File
  preview: string
  type: 'image' | 'video'
}

export default function UploadPage() {
  const { userData } = useAuth()
  const router = useRouter()

  // Form state
  const [caption, setCaption] = useState('')
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([])
  const [selectedNiche, setSelectedNiche] = useState('')
  const [visibility, setVisibility] = useState<VisibilityType>('circle-only')
  const [enableFeedback, setEnableFeedback] = useState(false)

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDraftSaving, setIsDraftSaving] = useState(false)

  // Form validation - check for selected files instead of uploaded files
  const hasSelectedFiles = mediaFiles.length > 0
  const isFormValid =
    caption.trim() !== '' && selectedNiche !== '' && hasSelectedFiles

  // Handle form submission
  const handleSubmit = async () => {
    if (!isFormValid) return

    setIsSubmitting(true)

    // Set up timeout for the entire operation
    const timeoutId = setTimeout(() => {
      setIsSubmitting(false)
      toast.error('Upload Timeout', {
        description:
          'The upload is taking too long. Please check your connection and try again.',
        duration: 5000,
      })
    }, 60000) // 60 second timeout

    try {
      // First upload the media files
      console.log('🚀 Starting media upload for final submission...')
      const filesToUpload = mediaFiles.map(mediaFile => mediaFile.file)

      const uploadedMediaArray = await uploadMultipleMedia(
        filesToUpload,
        userData?.uid || '',
        undefined, // postId - will be generated later
        () => {} // No progress tracking needed for now
      )

      // Clear timeout if upload succeeds
      clearTimeout(timeoutId)

      // Prepare submission data in the required format
      const submissionData = {
        title: caption,
        contents: uploadedMediaArray.map(
          (uploadedMedia: UploadedMedia, index: number) => ({
            contentType: uploadedMedia.fileType.startsWith('image/')
              ? 'image'
              : 'video',
            contentUrl: uploadedMedia.originalUrl,
            thumbnailUrl: uploadedMedia.thumbnailUrl,
            isMain: index === 0, // First item is main
          })
        ),
        contentCategoryId: selectedNiche,
        visibility: visibility === 'community' ? 'public' : 'circle',
      }

      // Beautiful logging with proper formatting
      console.log('\n' + '='.repeat(80))
      console.log('🎯 FINAL SUBMISSION DATA')
      console.log('='.repeat(80))
      console.log({ submissionData })
      console.log('='.repeat(80) + '\n')

      // Send to API
      const response = await NetworkManager.createSubmission(submissionData)

      console.log('✅ Post submitted successfully!', response.data)

      // Clear form on successful submission
      setCaption('')
      setMediaFiles([])
      setSelectedNiche('')
      setVisibility('circle-only')
      setEnableFeedback(false)

      // Show success toast
      toast.success('Post Submitted Successfully!', {
        description: 'Your post has been submitted successfully!',
        duration: 2000,
      })

      // Navigate to dashboard
      router.push('/dashboard')
    } catch (error: any) {
      // Clear timeout on error
      clearTimeout(timeoutId)

      console.error('❌ Submission error:', error)

      // Enhanced error handling with toast
      if (error.response) {
        const errorMessage =
          error.response.data?.error ||
          error.response.data?.message ||
          'Failed to submit post'
        toast.error('Submission Failed', {
          description: errorMessage,
          duration: 5000,
        })
      } else if (
        error.code === 'ECONNABORTED' ||
        error.message.includes('timeout')
      ) {
        toast.error('Upload Timeout', {
          description:
            'The upload took too long. Please check your connection and try again.',
          duration: 5000,
        })
      } else {
        toast.error('Submission Failed', {
          description: 'Please check your internet connection and try again.',
          duration: 5000,
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle draft saving
  const handleSaveDraft = async () => {
    if (!hasSelectedFiles) return
    setIsDraftSaving(true)

    // Set up timeout for the entire operation
    const timeoutId = setTimeout(() => {
      setIsDraftSaving(false)
      toast.error('Draft Save Timeout', {
        description:
          'The save is taking too long. Please check your connection and try again.',
        duration: 5000,
      })
    }, 60000) // 60 second timeout

    try {
      const filesToUpload = mediaFiles.map(mediaFile => mediaFile.file)

      const uploadedMediaArray = await uploadMultipleMedia(
        filesToUpload,
        userData?.uid || '',
        undefined, // postId - will be generated later
        () => {} // No progress tracking needed for now
      )

      // Clear timeout if upload succeeds
      clearTimeout(timeoutId)

      // Prepare submission data in the required format
      const submissionData = {
        title: caption,
        description: caption,
        campaignId: '',
        contents: uploadedMediaArray.map(
          (uploadedMedia: UploadedMedia, index: number) => ({
            contentType: uploadedMedia.fileType.startsWith('image/')
              ? 'image'
              : 'video',
            contentUrl: uploadedMedia.originalUrl,
            thumbnailUrl: uploadedMedia.thumbnailUrl,
            isMain: index === 0,
          })
        ),
        contentCategoryId: selectedNiche,
        visibility: visibility === 'community' ? 'public' : 'circle',
      }

      // Beautiful logging with proper formatting
      console.log('\n' + '='.repeat(80))
      console.log('🎯 FINAL SUBMISSION DATA')
      console.log('='.repeat(80))
      console.log({ submissionData })
      console.log('='.repeat(80) + '\n')

      // Send to API
      const response = await NetworkManager.createSubmission(submissionData)

      console.log('✅ Draft saved successfully!', { response: response.data })

      // Show success toast
      toast.success('Draft Saved Successfully!', {
        description: 'Your draft has been saved successfully!',
        duration: 2000,
      })

      // Navigate to dashboard
      router.push('/dashboard')
    } catch (error: any) {
      // Clear timeout on error
      clearTimeout(timeoutId)

      console.error('❌ Draft saving error:', error)

      // Enhanced error handling with toast
      if (error.response) {
        console.log('Error Status:', error.response.status)
        console.log('Error Data:', error.response.data)
        console.log('Error Headers:', error.response.headers)

        if (error.response.status === 403) {
          toast.error('Permission Denied', {
            description: 'Please check your account status and role.',
            duration: 5000,
          })
        } else {
          const errorMessage =
            error.response.data?.error ||
            error.response.data?.message ||
            'Failed to save draft'
          toast.error('Draft Save Failed', {
            description: errorMessage,
            duration: 5000,
          })
        }
      } else if (
        error.code === 'ECONNABORTED' ||
        error.message.includes('timeout')
      ) {
        toast.error('Draft Save Timeout', {
          description:
            'The save took too long. Please check your connection and try again.',
          duration: 5000,
        })
      } else {
        toast.error('Draft Save Failed', {
          description: 'Please check your internet connection and try again.',
          duration: 5000,
        })
      }
    } finally {
      setIsDraftSaving(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 min-w-[320px]">
      <div className="max-w-7xl mx-auto px-2 xs:px-4 sm:px-6 lg:px-8 py-4 xs:py-8">
        {/* Header */}
        {/* <div className="mb-4 xs:mb-8">
          <h1 className="text-xl xs:text-2xl sm:text-3xl text-center font-bold text-gray-900 mb-2">
            Create a Request for Feedback
          </h1>
          <p className="text-sm xs:text-base text-gray-600 text-center px-2">
            Share your content and get valuable feedback from our community
          </p>
        </div> */}

        {/* Main Content Card */}
        <div className="max-w-4xl mx-auto">
          <div className="rounded-xl xs:rounded-2xl bg-app-green shadow-lg overflow-hidden">
            {/* Header Bar */}
            <div className="mb-4 xs:mb-8">
              <h1 className="text-center font-bold text-white mt-8 text-[18px] leading-[22px]">
                Create a Request for Feedback
              </h1>
              <p className="text-white text-center my-2 px-8 text-[14px] leading-[20px]">
                Share your post with your circle or the community.
              </p>
            </div>

            {/* Form Content */}
            <div className="bg-app-green-light px-3 xs:px-4 sm:px-8 py-6 xs:py-8">
              <CaptionInput value={caption} onChange={setCaption} />

              <MediaUpload
                files={mediaFiles}
                onFilesChange={setMediaFiles}
                maxVideoDuration={30}
              />

              <NicheSelector
                selectedNiche={selectedNiche}
                onNicheChange={setSelectedNiche}
              />

              <VisibilitySettings
                visibility={visibility}
                onVisibilityChange={setVisibility}
                enableFeedback={enableFeedback}
                onEnableFeedbackChange={setEnableFeedback}
              />

              <ActionButtons
                onSaveDraft={handleSaveDraft}
                onSubmit={handleSubmit}
                isSubmitting={isSubmitting}
                isDraftSaving={isDraftSaving}
                isFormValid={isFormValid}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

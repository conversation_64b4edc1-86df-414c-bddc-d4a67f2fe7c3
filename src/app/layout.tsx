import type { Metadata } from 'next'
import { Gei<PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import { AuthProvider } from '@/contexts/AuthContext'
import { Toaster } from 'sonner'
import { ThemeProvider } from '@/components/theme-provider'
import { CategoryProvider } from '@/contexts/CategoryContext'
import { Suspense } from 'react'
import Loading from '@/components/shared/loading'
import { ModalProvider } from '@/contexts/ModalContext'
import { ResendEmailProvider } from '@/contexts/ResendEmailContext'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'CRITIQLE',
  description: 'CRITIQLE',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    minimumScale: 1,
    maximumScale: 5,
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable}`}
        suppressHydrationWarning
      >
        <ThemeProvider>
          <AuthProvider>
            <CategoryProvider>
              <ModalProvider>
                <ResendEmailProvider>
                  <Suspense fallback={<Loading />}>{children}</Suspense>
                </ResendEmailProvider>
                <Toaster position="bottom-right" />
              </ModalProvider>
            </CategoryProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}

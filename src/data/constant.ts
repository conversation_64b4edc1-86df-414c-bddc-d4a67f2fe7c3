import { TikTokIcon } from '@/components/ui/icons'
import {
  ClipboardList,
  Facebook,
  Instagram,
  Linkedin,
  Target,
  Twitter,
  Youtube,
} from 'lucide-react'

export const USER_ROLES = {
  creator: 'creator',
  critiquer: 'critiquer',
  admin: 'admin',
} as const

export type TRoles = keyof typeof USER_ROLES

export interface SocialHandle {
  platform: string
  username: string
}

///Critiquer sign up form constants
export const socialPlatforms = [
  { name: 'Instagram', icon: Instagram, color: '#E4405F' },
  { name: 'TikTok', icon: TikTokIcon, color: '#000000' },
  { name: 'YouTube', icon: Youtube, color: '#FF0000' },
  { name: 'Twitter', icon: Twitter, color: '#1DA1F2' },
  { name: 'Facebook', icon: Facebook, color: '#1877F2' },
  { name: 'LinkedIn', icon: Linkedin, color: '#0A66C2' },
]

export const stepIcons = [ClipboardList, Instagram, Target]

export const stepLabels = ['Information', 'Social', 'Niche']

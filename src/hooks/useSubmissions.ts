'use client'

import { useAuth } from '@/contexts/AuthContext'
import NetworkManager from '@/lib/axios/network-manager'
import {
  FirebaseTimestamp,
  Submission,
  SubmissionsResponse,
} from '@/types/submission'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

interface UseSubmissionsReturn {
  submissions: Submission[]
  loading: boolean
  error: string | null
  hasMore: boolean
  loadMore: () => void
  refresh: () => void
}

export const useSubmissions = (limit: number = 10): UseSubmissionsReturn => {
  const { userData, loading: authLoading } = useAuth()
  const [submissions, setSubmissions] = useState<Submission[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(false)
  const [nextCursor, setNextCursor] = useState<FirebaseTimestamp | null>(null)

  const fetchSubmissions = useCallback(
    async (cursor?: FirebaseTimestamp, append: boolean = false) => {
      try {
        if (!append) {
          setLoading(true)
        } else {
          setLoadingMore(true)
        }
        setError(null)

        const requestData = {
          limit,
          ...(cursor && { cursor: { lastCreatedAt: cursor } }),
        }

        const response = await NetworkManager.getUserSubmissions(requestData)
        const data: SubmissionsResponse = response.data

        if (append) {
          setSubmissions(prev => [...prev, ...data.posts])
        } else {
          setSubmissions(data.posts)
        }

        setHasMore(data.hasMore)
        setNextCursor(data.nextCursor)
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch submissions')
        toast.error('Failed to load submissions')
      } finally {
        setLoading(false)
        setLoadingMore(false)
      }
    },
    [limit]
  )

  const loadMore = useCallback(() => {
    if (hasMore && !loadingMore && nextCursor) {
      fetchSubmissions(nextCursor, true)
    }
  }, [hasMore, loadingMore, nextCursor, fetchSubmissions])

  const refresh = useCallback(() => {
    setSubmissions([])
    setNextCursor(null)
    fetchSubmissions()
  }, [fetchSubmissions])

  useEffect(() => {
    if (!authLoading && userData) {
      fetchSubmissions()
    } else if (!authLoading && !userData) {
      setLoading(false)
    }
  }, [fetchSubmissions, authLoading, userData])

  return {
    submissions,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
  }
}

import { useState, useEffect } from 'react'
import NetworkManager from '@/lib/axios/network-manager'

export interface Category {
  id: string
  categoryName: string
  categoryDescription: string
}

interface UseCategoriesReturn {
  categories: Category[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export const useCategories = (): UseCategoriesReturn => {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await NetworkManager.getContentCategories()

      if (response.data && Array.isArray(response.data)) {
        setCategories(response.data)
      } else {
        throw new Error('Invalid response format')
      }
    } catch (err) {
      console.error('Error fetching categories:', err)
      setError(
        err instanceof Error ? err.message : 'Failed to fetch categories'
      )

      // Fallback to default categories if API fails
      setCategories([
        {
          id: 'fallback-1',
          categoryName: 'Fashion & Style',
          categoryDescription: 'Fashion and style content',
        },
        {
          id: 'fallback-2',
          categoryName: 'Beauty & Skincare',
          categoryDescription: 'Beauty and skincare content',
        },
        {
          id: 'fallback-3',
          categoryName: 'Health & Fitness',
          categoryDescription: 'Health and fitness content',
        },
        {
          id: 'fallback-4',
          categoryName: 'Travel',
          categoryDescription: 'Travel content',
        },
        {
          id: 'fallback-5',
          categoryName: 'Food & Cooking',
          categoryDescription: 'Food and cooking content',
        },
        {
          id: 'fallback-6',
          categoryName: 'Tech & Gadgets',
          categoryDescription: 'Technology and gadgets content',
        },
        {
          id: 'fallback-7',
          categoryName: 'Home, Garden & DIY',
          categoryDescription: 'Home, garden and DIY content',
        },
      ])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
  }
}

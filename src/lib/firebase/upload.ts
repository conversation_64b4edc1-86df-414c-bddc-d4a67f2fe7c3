import { ref, uploadBytes, getDownloadURL, listAll } from 'firebase/storage'
import { storage } from './config'

/**
 * Test Firebase Storage connectivity
 */
export const testStorageConnectivity = async (): Promise<boolean> => {
  try {
    console.log('Testing Firebase Storage connectivity...')

    // Try to list files in the root directory (this is a lightweight operation)
    const rootRef = ref(storage, '/')
    await listAll(rootRef)

    console.log('Firebase Storage connectivity test passed')
    return true
  } catch (error) {
    console.error('Firebase Storage connectivity test failed:', error)
    return false
  }
}

export interface UploadedMedia {
  originalUrl: string
  thumbnailUrl: string
  fileName: string
  fileSize: number
  fileType: string
  dimensions?: {
    width: number
    height: number
  }
}

/**
 * Generate thumbnail for image files
 */
export const generateImageThumbnail = (
  file: File,
  maxWidth = 300,
  maxHeight = 300,
  quality = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    img.onload = () => {
      // Calculate thumbnail dimensions while maintaining aspect ratio
      let { width, height } = img

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }

      canvas.width = width
      canvas.height = height

      // Draw and compress the image
      ctx.drawImage(img, 0, 0, width, height)

      canvas.toBlob(
        blob => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to generate thumbnail'))
          }
        },
        'image/jpeg',
        quality
      )
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Generate thumbnail for video files
 */
export const generateVideoThumbnail = (
  file: File,
  maxWidth = 300,
  maxHeight = 300,
  quality = 0.8
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      reject(new Error('Canvas context not available'))
      return
    }

    video.onloadedmetadata = () => {
      // Set video to first frame
      video.currentTime = 1 // 1 second into the video
    }

    video.onseeked = () => {
      // Calculate thumbnail dimensions while maintaining aspect ratio
      let { videoWidth: width, videoHeight: height } = video

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width
          width = maxWidth
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height
          height = maxHeight
        }
      }

      canvas.width = width
      canvas.height = height

      // Draw video frame to canvas
      ctx.drawImage(video, 0, 0, width, height)

      canvas.toBlob(
        blob => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to generate video thumbnail'))
          }
        },
        'image/jpeg',
        quality
      )
    }

    video.onerror = () => reject(new Error('Failed to load video'))
    video.src = URL.createObjectURL(file)
    video.load()
  })
}

/**
 * Get image dimensions
 */
export const getImageDimensions = (
  file: File
): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image()

    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight })
    }

    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Get video dimensions
 */
export const getVideoDimensions = (
  file: File
): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video')

    video.onloadedmetadata = () => {
      resolve({ width: video.videoWidth, height: video.videoHeight })
    }

    video.onerror = () => reject(new Error('Failed to load video'))
    video.src = URL.createObjectURL(file)
    video.load()
  })
}

/**
 * Upload file to Firebase Storage with timeout and retry logic
 */
export const uploadFileToFirebase = async (
  file: File | Blob,
  path: string,
  fileName: string,
  maxRetries: number = 3
): Promise<string> => {
  let lastError: Error | null = null

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Upload attempt ${attempt}/${maxRetries} for ${fileName}`)

      const storageRef = ref(storage, `${path}/${fileName}`)

      // Upload without timeout - let Firebase handle it naturally
      const snapshot = await uploadBytes(storageRef, file)

      const downloadURL = await getDownloadURL(snapshot.ref)
      console.log(`Upload successful for ${fileName}`)
      return downloadURL
    } catch (error) {
      lastError = error as Error
      console.error(`Upload attempt ${attempt} failed for ${fileName}:`, error)

      if (attempt < maxRetries) {
        // Wait before retrying (exponential backoff)
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
        console.log(`Retrying in ${delay}ms...`)
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  throw new Error(
    `Upload failed after ${maxRetries} attempts: ${lastError?.message}`
  )
}

/**
 * Upload media file with thumbnail generation
 */
export const uploadMediaWithThumbnail = async (
  file: File,
  userId: string,
  postId?: string
): Promise<UploadedMedia> => {
  try {
    const isImage = file.type.startsWith('image/')
    const isVideo = file.type.startsWith('video/')

    if (!isImage && !isVideo) {
      throw new Error('File must be an image or video')
    }

    // Generate unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const baseFileName = `${timestamp}_${Math.random().toString(36).substring(7)}`
    const originalFileName = `${baseFileName}.${fileExtension}`
    const thumbnailFileName = `${baseFileName}_thumb.jpg`

    // Create submissions-based folder structure
    const mediaType = isImage ? 'images' : 'videos'
    const basePath = postId
      ? `submissions/posts/${postId}`
      : `submissions/${userId}/media/${mediaType}`
    const originalPath = `${basePath}/assets`
    const thumbnailPath = `${basePath}/thumbnails`

    // Get file dimensions
    const dimensions = isImage
      ? await getImageDimensions(file)
      : await getVideoDimensions(file)

    // Generate thumbnail
    const thumbnailBlob = isImage
      ? await generateImageThumbnail(file)
      : await generateVideoThumbnail(file)

    // Upload both files in parallel with better error handling
    console.log(`Starting upload for ${file.name} (${file.type})`)
    const [originalUrl, thumbnailUrl] = await Promise.all([
      uploadFileToFirebase(file, originalPath, originalFileName, 2), // 2 retries, no timeout
      uploadFileToFirebase(thumbnailBlob, thumbnailPath, thumbnailFileName, 2),
    ])
    console.log(`Upload completed for ${file.name}`)

    return {
      originalUrl,
      thumbnailUrl,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      dimensions,
    }
  } catch (error) {
    console.error('Upload failed:', error)
    throw error
  }
}

/**
 * Upload multiple media files with thumbnails
 */
export const uploadMultipleMedia = async (
  files: File[],
  userId: string,
  postId?: string,
  onProgress?: (
    progress: number,
    currentFile: number,
    totalFiles: number
  ) => void
): Promise<UploadedMedia[]> => {
  const results: UploadedMedia[] = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]

    try {
      onProgress?.(0, i + 1, files.length)

      const uploadedMedia = await uploadMediaWithThumbnail(file, userId, postId)
      results.push(uploadedMedia)

      onProgress?.(100, i + 1, files.length)
    } catch (error) {
      console.error(`Failed to upload file ${file.name}:`, error)
      throw error
    }
  }

  return results
}

import {
  ADD_ARTIFICIAL_USER,
  ADD_OR_UPDATE_USER,
  CHECK_USER_IN_AUTH_BY_EMAIL,
  CREATE_SUBMISSION,
  GET_CRITIQUER_CATEGORIES,
  GET_USER,
  GET_USER_SUBMISSIONS,
} from '@/shared/constants/constants'
import { SubmissionsRequest, SubmissionsResponse } from '@/types/submission'
import { UserData } from '@/types/user'
import { AxiosResponse } from 'axios'
import axios from './axios'

export default class NetworkManager {
  static async getUserFromFirestore(uid: string): Promise<AxiosResponse<any>> {
    return axios.get(`${GET_USER}?uid=${uid}`)
  }

  static async addUserToFirestore(data: UserData): Promise<AxiosResponse<any>> {
    return axios.post(`${ADD_OR_UPDATE_USER}`, data)
  }

  static async getContentCategories(): Promise<AxiosResponse<any>> {
    return axios.get(`${GET_CRITIQUER_CATEGORIES}`)
  }

  static async addArtificialUserToFirestore(
    data: UserData
  ): Promise<AxiosResponse<any>> {
    return axios.post(`${ADD_ARTIFICIAL_USER}`, data)
  }

  static async checkUserInAuthByEmail(
    userEmail: string
  ): Promise<AxiosResponse<any>> {
    return axios.get(`${CHECK_USER_IN_AUTH_BY_EMAIL}`, {
      params: { email: userEmail },
    })
  }

  static async createSubmission(data: any): Promise<AxiosResponse<any>> {
    return axios.post(`${CREATE_SUBMISSION}`, data)
  }

  static async getUserSubmissions(
    data: SubmissionsRequest
  ): Promise<AxiosResponse<SubmissionsResponse>> {
    return axios.post(`${GET_USER_SUBMISSIONS}`, data)
  }
}

import axios, { AxiosRequestConfig } from 'axios'
import { ContentType } from './axios.types'
import { auth } from '@/lib/firebase/config'

const getAxiosInstance = (
  config: AxiosRequestConfig = {
    headers: { contentType: ContentType.json },
  }
) => {
  const instance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API,
    headers: {
      'Content-Type': config.headers?.contentType || ContentType.json,
    },
  })

  // Add request interceptor to add Firebase ID token
  instance.interceptors.request.use(async config => {
    try {
      const user = auth.currentUser
      if (user) {
        const token = await user.getIdToken()
        config.headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      console.error('Error getting Firebase ID token:', error)
    }
    return config
  })

  return instance
}

export default getAxiosInstance()

'use client'
import React, { createContext, useContext, useState, ReactNode } from 'react'

interface ModalContextType {
  isCreatorModalOpen: boolean
  critiquerModalOpen: boolean
  setCreatorModalOpen: (open: boolean) => void
  setCritiquerModalOpen: (open: boolean) => void
}

const ModalContext = createContext<ModalContextType | undefined>(undefined)

export function ModalProvider({ children }: { children: ReactNode }) {
  const [isCreatorModalOpen, setCreatorModalOpen] = useState(false)
  const [critiquerModalOpen, setCritiquerModalOpen] = useState(false)

  return (
    <ModalContext.Provider
      value={{
        isCreatorModalOpen,
        critiquerModalOpen,
        setCritiquerModalOpen,
        setCreatorModalOpen,
      }}
    >
      {children}
    </ModalContext.Provider>
  )
}

export function useModal() {
  const context = useContext(ModalContext)
  if (context === undefined) {
    throw new Error('useModal must be used within a ModalProvider')
  }
  return context
}

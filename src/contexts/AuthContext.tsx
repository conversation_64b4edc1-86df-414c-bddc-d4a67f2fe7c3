'use client'
import React, { createContext, useContext, useEffect, useState } from 'react'
import {
  User,
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  sendEmailVerification,
  isSignInWithEmailLink,
  signInWithEmailLink,
  sendSignInLinkToEmail,
} from 'firebase/auth'
import { auth } from '@/lib/firebase/config'
import NetworkManager from '@/lib/axios/network-manager'
import { UserData } from '@/types/user'

interface AuthContextType {
  userData: UserData | null
  loading: boolean
  signUpWithEmailAndPassword: (
    email: string,
    password: string,
    additionalData: UserData
  ) => Promise<void>
  signInUserWithEmailAndPassword: (
    email: string,
    password: string
  ) => Promise<User>
  signUpWithGoogle: () => Promise<User>
  saveUserData: (data: UserData) => Promise<void>
  signInWithGoogle: () => Promise<void>
  logout: () => Promise<void>
  setUserData: (data: UserData) => void
  handlePasswordlessSignIn: (email?: string) => Promise<User | null>
  sendEmailVerificationLink: (email: string, isLogin?: boolean) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [userData, setUserData] = useState<any | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async user => {
      try {
        if (user) {
          setLoading(true)
          const response = await NetworkManager.getUserFromFirestore(user.uid)
          const userData = response.data
          setUserData(userData)
        } else {
          setUserData(null)
        }
      } catch (error: any) {
        setUserData(null)
      } finally {
        setLoading(false)
      }
    })

    return () => unsubscribe()
  }, [])

  const signUpWithEmailAndPassword = async (
    email: string,
    password: string,
    additionalData: UserData
  ) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      )

      await NetworkManager.addUserToFirestore({
        ...additionalData,
        uid: userCredential.user.uid,
        email: userCredential.user.email!,
      })

      await sendEmailVerification(userCredential.user)
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        throw new Error(
          'An account with this email already exists. Please sign in instead.'
        )
      }
      if (error.code === 'auth/invalid-email') {
        throw new Error('Please enter a valid email address.')
      }
      if (error.code === 'auth/weak-password') {
        throw new Error('Password should be at least 6 characters long.')
      }
      if (error.response?.status === 404) {
        throw new Error('Failed to create user in database')
      }
      throw error
    }
  }

  const signInUserWithEmailAndPassword = async (
    email: string,
    password: string
  ) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      )

      const response = await NetworkManager.getUserFromFirestore(
        userCredential.user.uid
      )

      if (!userCredential.user.emailVerified) {
        try {
          // Try to send verification email again

          await sendEmailVerification(userCredential.user)
          await signOut(auth)
          throw new Error(
            'Please verify your email before signing in. A new verification email has been sent to your inbox.'
          )
        } catch (verificationError: any) {
          // If we hit rate limit, just sign out without sending new verification
          if (verificationError.code === 'auth/too-many-requests') {
            await signOut(auth)
            throw new Error(
              'Please verify your email before signing in. Check your inbox for the verification email.'
            )
          }
          // For other errors, just sign out
          await signOut(auth)
          throw new Error(
            'Please verify your email before signing in. Check your inbox for the verification email.'
          )
        }
      }

      const userData = response.data
      setUserData(userData)
      return userCredential.user
    } catch (error: any) {
      if (error.code === 'auth/invalid-credential') {
        throw new Error(
          'Invalid email or password. Please check your credentials and try again.'
        )
      }
      if (error.response?.status === 404) {
        throw new Error('User not found in database')
      }
      throw error
    }
  }

  const signUpWithGoogle = async () => {
    try {
      const provider = new GoogleAuthProvider()
      provider.setCustomParameters({
        prompt: 'select_account',
      })
      const userCredential = await signInWithPopup(auth, provider)
      return userCredential.user
    } catch (error) {
      throw error
    }
  }

  const saveUserData = async (data: UserData) => {
    try {
      const response = await NetworkManager.addUserToFirestore({
        ...data,
      })

      const userData = response.data
      setUserData(userData.user)
    } catch (error) {
      throw error
    }
  }

  const signInWithGoogle = async () => {
    let userCredential
    try {
      const provider = new GoogleAuthProvider()
      userCredential = await signInWithPopup(auth, provider)

      const response = await NetworkManager.getUserFromFirestore(
        userCredential.user.uid
      )
      const userData = response.data
      setUserData(userData)
    } catch (error: any) {
      if (error.response?.status === 404 && userCredential) {
        throw new Error(
          `Google account ${userCredential.user.email} is not recognized for Google Sign-In on Critiqle. Please make sure you are using the same account that you have previously linked. OR complete your Google Sign-Up process first.`
        )
      } else {
        throw error
      }
    }
  }

  const sendEmailVerificationLink = async (
    email: string,
    isLogin?: boolean
  ) => {
    let url = `${process.env.NEXT_PUBLIC_REDIRECT_URL}?email=${encodeURIComponent(email)}`
    if (isLogin) {
      url += `&islogin=true`
    }
    const actionCodeSettings = {
      url,
      handleCodeInApp: true,
    }

    await sendSignInLinkToEmail(auth, email, actionCodeSettings)
  }

  const handlePasswordlessSignIn = async (
    emailParam?: string
  ): Promise<User | null> => {
    let email = emailParam
    if (isSignInWithEmailLink(auth, window.location.href) && email) {
      try {
        const result = await signInWithEmailLink(
          auth,
          email,
          window.location.href
        )
        return result.user
      } catch (error) {
        throw new Error(
          'The sign-in link is invalid or has expired. Please try again.'
        )
      }
    }
    return null
  }

  const logout = async () => {
    try {
      await signOut(auth)
    } catch (error) {
      throw error
    }
  }

  const value = {
    userData,
    loading,
    signUpWithEmailAndPassword,
    signInUserWithEmailAndPassword,
    signUpWithGoogle,
    saveUserData,
    signInWithGoogle,
    logout,
    setUserData,
    handlePasswordlessSignIn,
    sendEmailVerificationLink,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

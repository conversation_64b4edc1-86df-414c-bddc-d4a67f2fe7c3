'use client'
import React, {
  createContext,
  useContext,
  useState,
  useRef,
  useEffect,
} from 'react'

type ResendEmailContextType = {
  timer: number
  emailSent: boolean

  startTimer: (seconds: number) => void
  setEmailSent: (val: boolean) => void

  resetAll: () => void
}

const ResendEmailContext = createContext<ResendEmailContextType | undefined>(
  undefined
)

export const ResendEmailProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [timer, setTimer] = useState(0)
  const [emailSent, setEmailSent] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const startTimer = (seconds: number) => {
    setTimer(seconds)
  }

  const resetAll = () => {
    setTimer(0)
    setEmailSent(false)
  }

  useEffect(() => {
    if (timer > 0) {
      intervalRef.current = setInterval(() => {
        setTimer(prev => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!)
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [timer])

  return (
    <ResendEmailContext.Provider
      value={{
        timer,
        emailSent,
        startTimer,
        setEmailSent,
        resetAll,
      }}
    >
      {children}
    </ResendEmailContext.Provider>
  )
}

export const useResendEmail = () => {
  const context = useContext(ResendEmailContext)
  if (!context) {
    throw new Error('useResendEmail must be used within a ResendProvider')
  }
  return context
}

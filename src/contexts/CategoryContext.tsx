'use client'
import React, { createContext, useContext, useEffect, useState } from 'react'
import { Category, CategoryContextType } from '@/types/category'
import NetworkManager from '@/lib/axios/network-manager'

const CategoryContext = createContext<CategoryContextType | undefined>(
  undefined
)

export function CategoryProvider({ children }: { children: React.ReactNode }) {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = async () => {
    try {
      setLoading(true)
      const response = await NetworkManager.getContentCategories()
      setCategories(response.data)
      setError(null)
    } catch (err: any) {
      setError(err.message || 'Failed to fetch categories')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCategories()
  }, [])

  return (
    <CategoryContext.Provider
      value={{ categories, loading, error, fetchCategories }}
    >
      {children}
    </CategoryContext.Provider>
  )
}

export function useCategories() {
  const context = useContext(CategoryContext)
  if (context === undefined) {
    throw new Error('useCategories must be used within a CategoryProvider')
  }
  return context
}

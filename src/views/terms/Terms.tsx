import React from 'react'

const Terms = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-8">
          Terms of Service
        </h1>

        <div className="prose prose-lg max-w-none">
          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              1. Acceptance of Terms
            </h2>
            <p className="text-gray-700 mb-4">
              By accessing and using CRITIQLE, you accept and agree to be bound
              by the terms and provision of this agreement.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              2. Service Description
            </h2>
            <p className="text-gray-700 mb-4">
              CRITIQLE is a platform that connects content creators with
              verified critiquers who provide feedback on submitted content. Our
              service facilitates these connections and handles payment
              processing.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              3. User Responsibilities
            </h2>
            <p className="text-gray-700 mb-4">
              Users are responsible for maintaining the confidentiality of their
              account and password. You agree to accept responsibility for all
              activities that occur under your account.
            </p>
            <ul className="list-disc list-inside text-gray-700 mb-4">
              <li>Provide accurate and complete information</li>
              <li>Maintain the security of your account</li>
              <li>Comply with all applicable laws and regulations</li>
              <li>Respect intellectual property rights</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              4. Content Guidelines
            </h2>
            <p className="text-gray-700 mb-4">
              All content submitted must be original or properly licensed.
              Prohibited content includes but is not limited to:
            </p>
            <ul className="list-disc list-inside text-gray-700 mb-4">
              <li>Illegal, harmful, or offensive material</li>
              <li>Copyright infringing content</li>
              <li>Spam or misleading information</li>
              <li>Content that violates privacy rights</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              5. Payment Terms
            </h2>
            <p className="text-gray-700 mb-4">
              Payment is required before critique services are provided. Refunds
              may be available under specific circumstances as outlined in our
              refund policy.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              6. Limitation of Liability
            </h2>
            <p className="text-gray-700 mb-4">
              CRITIQLE shall not be liable for any indirect, incidental,
              special, consequential, or punitive damages resulting from your
              use of the service.
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-4">
              7. Changes to Terms
            </h2>
            <p className="text-gray-700 mb-4">
              We reserve the right to modify these terms at any time. Users will
              be notified of significant changes via email or platform
              notification.
            </p>
          </section>

          <section className="mb-8">
            <p className="text-gray-600 text-sm">Last updated: December 2024</p>
          </section>
        </div>
      </div>
    </div>
  )
}

export default Terms

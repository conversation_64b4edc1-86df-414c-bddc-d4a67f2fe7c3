'use client'
import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Camera, Loader2, Plus, X, CreditCard } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { UserData } from '@/types/user'
import { useCategories } from '@/contexts/CategoryContext'
import { toast } from 'sonner'
import NetworkManager from '@/lib/axios/network-manager'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject,
  getMetadata,
} from 'firebase/storage'
import { storage } from '@/lib/firebase/config'
import { USER_ROLES } from '@/data/constant'

const socialPlatforms = [
  'Instagram',
  'TikTok',
  'YouTube',
  'Twitter',
  'Facebook',
  'LinkedIn',
]

const Profile = () => {
  const { userData, setUserData } = useAuth()
  const { categories } = useCategories()
  const [userInfo, setUserInfo] = useState<UserData>({
    email: userData?.email || '',
    fullName: userData?.fullName || '',
    role: userData?.role || '',
    socialLinks: userData?.socialLinks || [],
    bio: userData?.bio || '',
    businessName: userData?.businessName || '',
    brandName: userData?.brandName || '',
    phoneNumber: userData?.phoneNumber || '',
    profilePicture: userData?.profilePicture || '',
    critiquerCategory: userData?.critiquerCategory || [],
  })
  const [initialUserInfo, setInitialUserInfo] = useState<UserData>({
    email: userData?.email || '',
    fullName: userData?.fullName || '',
    role: userData?.role || '',
    socialLinks: userData?.socialLinks || [],
    bio: userData?.bio || '',
    businessName: userData?.businessName || '',
    brandName: userData?.brandName || '',
    phoneNumber: userData?.phoneNumber || '',
    profilePicture: userData?.profilePicture || '',
    critiquerCategory: userData?.critiquerCategory || [],
  })
  const [socialHandles, setSocialHandles] = useState<
    { platform: string; username: string }[]
  >(
    userData?.socialLinks
      ? Object.entries(userData.socialLinks).map(([platform, username]) => ({
          platform,
          username: (username as string).replace('@', ''),
        }))
      : []
  )
  const [initialSocialHandles, setInitialSocialHandles] = useState<
    { platform: string; username: string }[]
  >(
    userData?.socialLinks
      ? Object.entries(userData.socialLinks).map(([platform, username]) => ({
          platform,
          username: (username as string).replace('@', ''),
        }))
      : []
  )
  const [isDirty, setIsDirty] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isProfilePictureUploading, setIsProfilePictureUploading] =
    useState(false)
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [initialSelectedCategories, setInitialSelectedCategories] = useState<
    string[]
  >([])
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [notifications, setNotifications] = useState({
    emailCritiques: true,
    emailMatches: true,
    weeklyReports: false,
    marketingEmails: false,
    smsUpdates: false,
  })

  useEffect(() => {
    if (userData) {
      const initialData = {
        email: userData.email || '',
        fullName: userData.fullName || '',
        role: userData.role || '',
        socialLinks: userData.socialLinks || [],
        bio: userData.bio || '',
        businessName: userData.businessName || '',
        brandName: userData.brandName || '',
        phoneNumber: userData.phoneNumber || '',
        profilePicture: userData.profilePicture || '',
        critiquerCategory: userData.critiquerCategory || [],
      }

      setUserInfo(initialData)
      setInitialUserInfo(initialData)

      if (userData.socialLinks && Array.isArray(userData.socialLinks)) {
        const handles = userData.socialLinks.map((link: string) => {
          const firstColonIndex = link.indexOf(':')
          const platform = link.substring(0, firstColonIndex)
          const username = link.substring(firstColonIndex + 1)
          return {
            platform,
            username: username.replace('@', ''),
          }
        })
        setSocialHandles(handles)
        setInitialSocialHandles(handles)
      }

      if (userData.critiquerCategory) {
        setSelectedCategories(userData.critiquerCategory)
        setInitialSelectedCategories(userData.critiquerCategory)
      }
    }
  }, [userData])

  useEffect(() => {
    const hasUserInfoChanged =
      JSON.stringify(userInfo) !== JSON.stringify(initialUserInfo)
    const hasSocialHandlesChanged =
      JSON.stringify(socialHandles) !== JSON.stringify(initialSocialHandles)
    const hasCategoriesChanged =
      JSON.stringify(selectedCategories) !==
      JSON.stringify(initialSelectedCategories)
    setIsDirty(
      hasUserInfoChanged || hasSocialHandlesChanged || hasCategoriesChanged
    )
  }, [
    userInfo,
    socialHandles,
    selectedCategories,
    initialUserInfo,
    initialSocialHandles,
    initialSelectedCategories,
  ])

  const handleUserInfoChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target
    setUserInfo(prev => ({ ...prev, [name]: value }))
  }

  const handleAddSocialHandle = () => {
    setSocialHandles([...socialHandles, { platform: '', username: '' }])
  }

  const handleRemoveSocialHandle = (index: number) => {
    setSocialHandles(socialHandles.filter((_, i) => i !== index))
  }

  const handleSocialHandleChange = (
    index: number,
    field: 'platform' | 'username',
    value: string
  ) => {
    const newHandles = [...socialHandles]
    newHandles[index] = { ...newHandles[index], [field]: value }
    setSocialHandles(newHandles)
  }

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        if (prev.length === 1) {
          return prev
        }
        return prev.filter(id => id !== categoryId)
      } else if (prev.length < 2) {
        return [...prev, categoryId]
      } else {
        return prev
      }
    })
  }

  const handleNotificationChange = (
    key: keyof typeof notifications,
    value: boolean
  ) => {
    setNotifications(prev => ({ ...prev, [key]: value }))
  }

  const handleSaveChanges = async () => {
    try {
      setIsLoading(true)
      // Convert social handles to array format with platform:username format
      const socialLinks = socialHandles.map(
        handle =>
          `${handle.platform}:${handle.username.startsWith('@') ? handle.username : `@${handle.username}`}`
      )

      const updatedUserInfo: UserData = {
        ...userInfo,
        email: userData?.email || '',
        uid: userData?.uid || '',
        role: userData?.role || '',
        socialLinks: socialLinks,
        critiquerCategory: selectedCategories,
      }

      const response = await NetworkManager.addUserToFirestore(updatedUserInfo)
      if (response.data) {
        // Create updated data with all fields
        const updatedData = {
          ...response.data,
          email: userData?.email || '',
          uid: userData?.uid || '',
          role: userData?.role || '',
          fullName: response.data.fullName || userInfo.fullName,
          bio: response.data.bio || userInfo.bio,
          businessName: response.data.businessName || userInfo.businessName,
          brandName: response.data.brandName || userInfo.brandName,
          phoneNumber: response.data.phoneNumber || userInfo.phoneNumber,
          profilePicture:
            response.data.profilePicture || userInfo.profilePicture,
          socialLinks: response.data.socialLinks || socialLinks,
          critiquerCategory:
            response.data.critiquerCategory || selectedCategories,
        }

        // Update local state
        setUserInfo(updatedData)
        setInitialUserInfo(updatedData)

        // Update social handles
        if (updatedData.socialLinks && Array.isArray(updatedData.socialLinks)) {
          const handles = updatedData.socialLinks.map((link: string) => {
            const firstColonIndex = link.indexOf(':')
            const platform = link.substring(0, firstColonIndex)
            const username = link.substring(firstColonIndex + 1)
            return {
              platform,
              username: username.replace('@', ''),
            }
          })
          setSocialHandles(handles)
          setInitialSocialHandles(handles)
        }

        // Update categories for critiquer
        if (updatedData.role === 'critiquer') {
          const categories = updatedData.critiquerCategory || []
          setSelectedCategories(categories)
          setInitialSelectedCategories(categories)
        }

        // Update context
        setUserData(updatedData)

        toast.success('Profile updated successfully')
        setIsDirty(false)
      }
    } catch (error) {
      toast.error('Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size should be less than 5MB')
      return
    }
    // Validate file type (JPG, PNG, WebP)
    if (!file.type.match(/^image\/(jpeg|png|webp)$/)) {
      toast.error('Only JPG, PNG, and WebP images are allowed')
      return
    }

    try {
      setIsProfilePictureUploading(true)

      if (
        userInfo.profilePicture &&
        userInfo.profilePicture.includes('firebasestorage.googleapis.com')
      ) {
        const oldImageRef = ref(storage, userInfo.profilePicture)
        await deleteObject(oldImageRef)
      }

      // Upload new image
      const storageRef = ref(
        storage,
        `profile-pictures/${userData?.uid}/${file.name}`
      )
      const snapshot = await uploadBytes(storageRef, file)
      const imageUrl = await getDownloadURL(snapshot.ref)

      // Update userInfo with the new image URL
      setUserInfo(prev => ({ ...prev, profilePicture: imageUrl }))
      setIsDirty(true)
      toast.success('Profile picture uploaded successfully')
    } catch (error) {
      toast.error('Failed to upload profile picture')
    } finally {
      setIsProfilePictureUploading(false)
    }
  }

  const isCreator = userInfo.role === USER_ROLES.creator

  // Disable save if any social handle is incomplete
  const isAnySocialHandleIncomplete = socialHandles.some(
    handle => !handle.platform || !handle.username.trim()
  )

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Profile Settings
          </h1>
          <p className="text-gray-600">
            Manage your account information and preferences
          </p>
        </div>

        <Tabs defaultValue="personal" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="personal" disabled>
              Personal Info
            </TabsTrigger>
            <TabsTrigger value="notifications" disabled>
              Notifications
            </TabsTrigger>
            <TabsTrigger value="security" disabled>
              Security
            </TabsTrigger>
            <TabsTrigger value="billing" disabled>
              Billing
            </TabsTrigger>
          </TabsList>

          <TabsContent value="personal" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile Picture</CardTitle>
                <CardDescription>Update your profile picture</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-6">
                  <Avatar className="h-24 w-24">
                    <AvatarImage
                      src={userInfo.profilePicture || '/images/placeholder.jpg'}
                    />
                    <AvatarFallback className="text-xl">
                      {userInfo.fullName?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept="image/jpeg,image/png,image/webp"
                      onChange={handleImageUpload}
                    />
                    <Button
                      variant="outline"
                      className="mb-2"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isLoading}
                    >
                      {isProfilePictureUploading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Camera className="h-4 w-4 mr-2" />
                      )}
                      Change Photo
                    </Button>
                    <p className="text-sm text-gray-500">
                      JPG, PNG, or WebP. 5MB max.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
                <CardDescription>Update your personal details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={userInfo.fullName || ''}
                    onChange={handleUserInfoChange}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    value={userInfo.email || ''}
                    onChange={handleUserInfoChange}
                    disabled
                    className="bg-gray-100"
                  />
                </div>
                <div>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    name="phoneNumber"
                    value={userInfo.phoneNumber || ''}
                    onChange={handleUserInfoChange}
                  />
                </div>

                {isCreator ? (
                  <>
                    <div>
                      <Label htmlFor="businessName">Business Name</Label>
                      <Input
                        id="businessName"
                        name="businessName"
                        value={userInfo.businessName || ''}
                        onChange={handleUserInfoChange}
                      />
                    </div>
                    <div>
                      <Label htmlFor="brandName">Brand Name</Label>
                      <Input
                        id="brandName"
                        name="brandName"
                        value={userInfo.brandName || ''}
                        onChange={handleUserInfoChange}
                      />
                    </div>
                  </>
                ) : (
                  <>
                    <div>
                      <Label>Social Media Handles</Label>
                      <div className="space-y-2">
                        {socialHandles.map((handle, index) => (
                          <div key={index} className="flex gap-2">
                            <Select
                              value={handle.platform}
                              onValueChange={value =>
                                handleSocialHandleChange(
                                  index,
                                  'platform',
                                  value
                                )
                              }
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Select platform" />
                              </SelectTrigger>
                              <SelectContent>
                                {socialPlatforms.map(platform => (
                                  <SelectItem key={platform} value={platform}>
                                    {platform}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Input
                              value={handle.username}
                              onChange={e =>
                                handleSocialHandleChange(
                                  index,
                                  'username',
                                  e.target.value
                                )
                              }
                              placeholder="Username"
                              className="flex-1"
                            />
                            {socialHandles.length > 1 && (
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleRemoveSocialHandle(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleAddSocialHandle}
                          className="w-full"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Social Media Handle
                        </Button>
                      </div>
                    </div>
                    <div>
                      <Label>Expert Categories</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {categories.map(category => (
                          <Badge
                            key={category.id}
                            variant={
                              selectedCategories.includes(category.id)
                                ? 'default'
                                : 'outline'
                            }
                            className="cursor-pointer"
                            onClick={() => handleCategoryChange(category.id)}
                          >
                            {category.categoryName}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {isCreator && (
                  <div>
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      name="bio"
                      value={userInfo.bio || ''}
                      onChange={handleUserInfoChange}
                      placeholder="Tell us about yourself..."
                    />
                  </div>
                )}

                <Button
                  onClick={handleSaveChanges}
                  disabled={
                    !isDirty || isLoading || isAnySocialHandleIncomplete
                  }
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>
                  Update your password to keep your account secure
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input id="currentPassword" type="password" />
                </div>
                <div>
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input id="newPassword" type="password" />
                </div>
                <div>
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input id="confirmPassword" type="password" />
                </div>
                <Button className="bg-indigo-600 hover:bg-indigo-700">
                  Update Password
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Two-Factor Authentication</CardTitle>
                <CardDescription>
                  Add an extra layer of security to your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">2FA Status</p>
                    <p className="text-sm text-gray-500">Currently disabled</p>
                  </div>
                  <Button variant="outline">Enable 2FA</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Email Notifications</CardTitle>
                <CardDescription>
                  Choose which emails you'd like to receive
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Critique Completions</p>
                    <p className="text-sm text-gray-500">
                      Get notified when your critiques are ready
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notifications.emailCritiques}
                    onChange={e =>
                      handleNotificationChange(
                        'emailCritiques',
                        e.target.checked
                      )
                    }
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">New Matches</p>
                    <p className="text-sm text-gray-500">
                      When critiquers accept your submissions
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notifications.emailMatches}
                    onChange={e =>
                      handleNotificationChange('emailMatches', e.target.checked)
                    }
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Weekly Reports</p>
                    <p className="text-sm text-gray-500">
                      Weekly summary of your activity
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notifications.weeklyReports}
                    onChange={e =>
                      handleNotificationChange(
                        'weeklyReports',
                        e.target.checked
                      )
                    }
                    className="rounded"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Marketing Emails</p>
                    <p className="text-sm text-gray-500">
                      Tips, feature updates, and promotional content
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notifications.marketingEmails}
                    onChange={e =>
                      handleNotificationChange(
                        'marketingEmails',
                        e.target.checked
                      )
                    }
                    className="rounded"
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>SMS Notifications</CardTitle>
                <CardDescription>
                  Receive important updates via text message
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Critical Updates</p>
                    <p className="text-sm text-gray-500">
                      Urgent notifications about your submissions
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={notifications.smsUpdates}
                    onChange={e =>
                      handleNotificationChange('smsUpdates', e.target.checked)
                    }
                    className="rounded"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="billing" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Current Plan</CardTitle>
                <CardDescription>
                  Manage your subscription and billing information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="font-medium">Free Plan</p>
                    <p className="text-sm text-gray-500">
                      5 submissions per month
                    </p>
                  </div>
                  <Badge variant="secondary">Current Plan</Badge>
                </div>
                <Button variant="outline" className="w-full">
                  Upgrade to Pro
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Method</CardTitle>
                <CardDescription>
                  Add or update your payment information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 border rounded-lg">
                    <CreditCard className="h-6 w-6 text-gray-400" />
                    <div className="flex-1">
                      <p className="font-medium">No payment method on file</p>
                      <p className="text-sm text-gray-500">
                        Add a payment method to upgrade your plan
                      </p>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    Add Payment Method
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Billing History</CardTitle>
                <CardDescription>View your past transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-6">
                  <p className="text-gray-500">No billing history available</p>
                  <p className="text-sm text-gray-400">
                    Your transactions will appear here when you upgrade
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default Profile

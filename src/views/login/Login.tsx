'use client'
import React, { useEffect, useState, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import Image from 'next/image'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import Loading from '@/components/shared/loading'
import NetworkManager from '@/lib/axios/network-manager'
import { useResendEmail } from '@/contexts/ResendEmailContext'
import { RESEND_EMAIL_TIME } from '@/shared/constants/constants'

const Login = () => {
  const router = useRouter()
  const {
    userData,
    loading: isAuthenticatingUser,
    sendEmailVerificationLink,
  } = useAuth()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })
  const { timer, emailSent, startTimer, setEmailSent } = useResendEmail()

  useEffect(() => {
    if (!isAuthenticatingUser && userData) {
      router.replace('/dashboard')
    }
  }, [userData, isAuthenticatingUser, router])

  if (isAuthenticatingUser || userData) {
    return <Loading />
  }

  const sendVerification = async () => {
    try {
      await NetworkManager.checkUserInAuthByEmail(formData.email)
      await sendEmailVerificationLink(formData.email, true)
      setEmailSent(true)
      startTimer(RESEND_EMAIL_TIME)
      toast.success('Please check your email for verification Link', {
        icon: <div className="text-green-500">✓</div>,
      })
    } catch (error: any) {
      toast.error(
        error.response?.data?.error || error.message || 'Something went wrong!',
        {
          icon: <div className="text-red-600">✕</div>,
        }
      )
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    await sendVerification()
    setLoading(false)
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="h-[calc(100vh-70px)] bg-gray-50 flex flex-col   sm:px-6 lg:px- py-20">
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex items-center justify-center mb-8">
          <Image
            src="/images/logo.jpeg"
            alt="critiqle logo"
            width={140}
            height={70}
            style={{ height: '70px', objectFit: 'contain' }}
          />
        </div>
        <div className=" bg-[#eaf7f0] py-8 pb-12 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="sm:mx-auto sm:w-full sm:max-w-md"></div>
          <div className="sm:mx-auto sm:w-full sm:max-w-md">
            <h2 className="text-center text-3xl font-bold text-gray-900 mb-2">
              Sign in to CRITIQLE
            </h2>
            <p className="text-center text-sm text-gray-600">
              Welcome back! Please sign in to your account.
            </p>
          </div>
          <div className="space-y-6 mt-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {!emailSent || (emailSent && timer === 0) ? (
                <>
                  <div>
                    <Label htmlFor="email">Email address</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Enter your email"
                      value={formData.email}
                      onChange={e => handleInputChange('email', e.target.value)}
                      required
                      className="mt-1"
                      disabled={loading}
                    />
                  </div>
                  <Button
                    type="submit"
                    className="w-full "
                    disabled={loading || !formData.email}
                  >
                    {loading
                      ? 'Sending Verification Link...'
                      : 'Send Verification Link'}
                  </Button>
                </>
              ) : (
                <div className="text-center space-y-2">
                  <div className="text-green-700 font-semibold text-lg">
                    Email sent successfully. Please check your inbox for the
                    verification link.
                  </div>
                  <div className="text-indigo-500 text-sm">
                    You can resend again in{' '}
                    <span className="font-semibold">{timer}s</span>
                  </div>
                </div>
              )}
            </form>

            <div className="text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{' '}
                <Link
                  href="/"
                  className="text-indigo-600 hover:text-indigo-500 font-medium"
                >
                  Sign up
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login

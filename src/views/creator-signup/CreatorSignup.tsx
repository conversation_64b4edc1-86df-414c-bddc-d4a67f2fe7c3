'use client'
import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { toast } from '@/components/ui/sonner'
import { useRouter } from 'next/navigation'
import { Loader } from 'lucide-react'
import NetworkManager from '@/lib/axios/network-manager'
import { USER_ROLES } from '@/data/constant'
import { useAuth } from '@/contexts/AuthContext'

const initialForm = {
  fullName: '',
  email: '',
  businessName: '',
  brandName: '',
}

const CreatorSignup = () => {
  const [form, setForm] = useState(initialForm)
  const [loading, setLoading] = useState(false)
  const [submitted, setSubmitted] = useState(false)
  const router = useRouter()
  const { sendEmailVerificationLink } = useAuth()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm(prev => ({ ...prev, [e.target.name]: e.target.value }))
  }

  const isFormValid = () => {
    return (
      form.fullName.trim() !== '' &&
      form.email.trim() !== '' &&
      form.businessName.trim() !== '' &&
      form.brandName.trim() !== ''
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    try {
      await NetworkManager.addArtificialUserToFirestore({
        fullName: form.fullName,
        email: form.email,
        role: USER_ROLES.creator,
        businessName: form.businessName,
        brandName: form.brandName,
      })
      await sendEmailVerificationLink(form.email)
      setSubmitted(true)
    } catch (error: any) {
      if (error.status === 409) {
        toast.error('User already exists. Try a different email.', {
          icon: <div className="text-red-500">✕</div>,
        })
      } else {
        toast.error(error.message, {
          icon: <div className="text-red-500">✕</div>,
        })
      }
    } finally {
      setLoading(false)
    }
  }

  const handleContinue = () => {
    router.push('/')
  }

  const cardWidth = 'w-[90%] max-w-[768px]'

  return (
    <div
      className="flex flex-col items-center justify-start bg-background py-12"
      style={{ minHeight: 'calc(100vh - 64px)' }}
    >
      <h1 className="w-[90%] text-3xl md:text-4xl font-bold mb-8 text-center">
        Join <span className="text-green-600">CRITIQLE</span> as a Creator
      </h1>
      <Card
        className={`shadow-lg ${cardWidth} ${submitted ? 'py-12' : ''} bg-green-50`}
      >
        <CardContent className="px-8 py-10">
          {!submitted ? (
            <form className="space-y-8" onSubmit={handleSubmit}>
              <div>
                <Label htmlFor="fullName">Full Name*</Label>
                <Input
                  id="fullName"
                  name="fullName"
                  placeholder="Enter full name"
                  value={form.fullName}
                  onChange={handleChange}
                  disabled={loading}
                  autoComplete="off"
                />
              </div>
              <div>
                <Label htmlFor="email">Email*</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={form.email}
                  onChange={handleChange}
                  disabled={loading}
                  autoComplete="off"
                />
              </div>
              <div>
                <Label htmlFor="business">Business/Company *</Label>
                <Input
                  id="business"
                  name="businessName"
                  placeholder="Enter your business or company"
                  value={form.businessName}
                  onChange={handleChange}
                  disabled={loading}
                  autoComplete="off"
                />
              </div>
              <div>
                <Label htmlFor="brand">Brand Name *</Label>
                <Input
                  id="brand"
                  name="brandName"
                  placeholder="Enter your brand name"
                  value={form.brandName}
                  onChange={handleChange}
                  disabled={loading}
                  autoComplete="off"
                />
              </div>
              <Button
                type="submit"
                className={`w-full mt-8 text-white ${!isFormValid() || loading ? 'bg-[#C1C1C1] cursor-not-allowed' : 'bg-black hover:bg-gray-900'}`}
                disabled={loading || !isFormValid()}
              >
                {loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader className="animate-spin w-5 h-5" />
                    Submitting...
                  </span>
                ) : (
                  'Submit'
                )}
              </Button>
            </form>
          ) : (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="rounded-full bg-green-100 p-6 mb-6">
                <svg
                  width="48"
                  height="48"
                  viewBox="0 0 48 48"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="24"
                    cy="24"
                    r="24"
                    fill="#4ADE80"
                    fillOpacity="0.2"
                  />
                  <path
                    d="M16 24L22 30L32 18"
                    stroke="#22C55E"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center">
                Thanks for Applying!
              </h2>
              <h4 className="text-xl font-semibold mb-2 text-center">
                Please check your email for verification link!
              </h4>
              <p className="text-center text-gray-600 mb-6 text-sm">
                We are reviewing your information, and you can expect a response
                in 24 hours.
              </p>
              <Button
                className="w-full mt-4 bg-black text-white hover:bg-gray-900"
                onClick={handleContinue}
              >
                Continue
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default CreatorSignup

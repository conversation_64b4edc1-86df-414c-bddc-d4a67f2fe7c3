'use client'
import React, { useEffect, useState } from 'react'
import { getAuth, applyActionCode } from 'firebase/auth'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { toast, ToastT } from 'sonner'
import NetworkManager from '@/lib/axios/network-manager'

const EmailVerification = () => {
  const [status, setStatus] = useState<
    | 'verifying'
    | 'success'
    | 'expired'
    | 'error'
    | 'signingIn'
    | 'signInSuccess'
    | 'signInError'
  >('verifying')
  const [message, setMessage] = useState('')
  const { handlePasswordlessSignIn, userData, setUserData, loading } = useAuth()
  const router = useRouter()
  const [signedIn, setSignedIn] = useState(false)

  useEffect(() => {
    if (signedIn && !loading && userData) {
      router.push('/dashboard')
    }
  }, [signedIn, loading, userData, router])

  useEffect(() => {
    if (status !== 'verifying') {
      return
    }

    const auth = getAuth()
    const queryParams = new URLSearchParams(window.location.search)
    const oobCode = queryParams.get('oobCode')
    const mode = queryParams.get('mode')
    const continueUrl = queryParams.get('continueUrl')
    if (mode === 'verifyEmail' && oobCode) {
      applyActionCode(auth, oobCode)
        .then(() => {
          setStatus('success')
          setMessage(
            'Your email has been successfully verified! You can now log in.'
          )
        })
        .catch(error => {
          if (
            error.code === 'auth/expired-action-code' ||
            error.message?.includes('invalid')
          ) {
            setStatus('expired')
            setMessage(
              'This verification link has expired. Please request a new verification email.'
            )
          } else {
            setStatus('error')
            setMessage(
              'Invalid or malformed verification link. Please check the URL or try again.'
            )
          }
        })
    } else if (mode === 'signIn') {
      setStatus('signingIn')
      setMessage('Signing you in...')
      ;(async () => {
        let email = undefined
        try {
          let islogin = undefined
          if (continueUrl) {
            try {
              const urlObj = new URL(continueUrl)
              email = urlObj.searchParams.get('email') || undefined
              islogin = urlObj.searchParams.get('islogin') || undefined
            } catch (e) {
              const params = new URLSearchParams(continueUrl.split('?')[1])
              email = params.get('email') || undefined
              islogin = params.get('islogin') || undefined
            }
          }
          const user = await handlePasswordlessSignIn(email)
          if (user) {
            setSignedIn(true)
          }
          if (islogin && user) {
            toast.error('Successfully signed In', {
              icon: <div className="text-green-500">✓</div>,
              duration: 3000,
            } as ToastT)
            router.push('dashboard')
          } else {
            setStatus('success')
            setMessage('Your email has been successfully verified!')
            if (!userData) {
              const response = await NetworkManager.getUserFromFirestore(
                user!.uid
              )
              setUserData(response.data)
            }
          }
        } catch (error: any) {
          setStatus('error')
          if (error.status === 404) {
            setMessage(
              `This ${email} account is not linked with Critical Yet. Please complete the sign up first.`
            )
            toast.error(
              `This ${email} account is not linked with Critical Yet. Please complete the sign up first.`,
              {
                icon: <div className="text-red-500">✓</div>,
                duration: 3000,
              } as ToastT
            )
          }
          setMessage(error.message)
          setTimeout(() => {
            router.push('/')
          }, 3000)
        }
      })()
    } else {
      setStatus('error')
      setMessage(
        'Invalid request. The verification link is missing or incorrect.'
      )
      setTimeout(() => {
        router.push('/')
      }, 4000)
    }
  }, [handlePasswordlessSignIn, router, status])

  const renderContent = () => {
    switch (status) {
      case 'verifying':
        return <p className="text-gray-600 text-lg">Verifying...</p>
      case 'signingIn':
        return <p className="text-gray-600 text-lg">{message}</p>
      case 'success':
        return (
          <>
            <h2 className="text-green-600 text-2xl font-semibold mb-2">
              Email Verified 🎉
            </h2>
            <p className="text-gray-700">{message}</p>
          </>
        )
      case 'signInSuccess':
        return (
          <>
            <h2 className="text-green-600 text-2xl font-semibold mb-2">
              Sign In Successful 🎉
            </h2>
            <p className="text-gray-700">{message}</p>
          </>
        )
      case 'expired':
        return (
          <>
            <h2 className="text-yellow-600 text-2xl font-semibold mb-2">
              Link Expired ⚠️
            </h2>
            <p className="text-gray-700">{message}</p>
          </>
        )
      case 'signInError':
      case 'error':
      default:
        return (
          <>
            <h2 className="text-red-600 text-2xl font-semibold mb-2">
              Verification Failed ❌
            </h2>
            <p className="text-gray-700">{message}</p>
          </>
        )
    }
  }

  return (
    <div className="flex items-center justify-center h-screen px-4 bg-gray-50">
      <div className="bg-white shadow-md rounded-xl p-6 max-w-md w-full text-center">
        {renderContent()}
      </div>
    </div>
  )
}

export default EmailVerification

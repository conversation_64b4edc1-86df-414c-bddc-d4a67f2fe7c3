'use client'
import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, X, Check } from 'lucide-react'
import { useRouter } from 'next/navigation'
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from '@/components/ui/select'
import { useCategories } from '@/contexts/CategoryContext'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'sonner'
import NetworkManager from '@/lib/axios/network-manager'
import {
  SocialHandle,
  socialPlatforms,
  stepIcons,
  stepLabels,
  USER_ROLES,
} from '@/data/constant'

const CritiqueurSignup = () => {
  const { categories, error: categoriesError } = useCategories()
  const { sendEmailVerificationLink } = useAuth()
  const [currentStep, setCurrentStep] = useState(1)
  const [termsAccepted, setTermsAccepted] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [emailError, setEmailError] = useState('')
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    selectedCategory: '',
    socialHandles: [{ platform: '', username: '' }],
  })
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    const socialLinks = formData.socialHandles.map(
      handle => `${handle.platform}:@${handle.username}`
    )
    try {
      await NetworkManager.addArtificialUserToFirestore({
        email: formData.email,
        fullName: `${formData.firstName} ${formData.lastName}`.trim(),
        role: USER_ROLES.critiquer,
        socialLinks,
        critiquerCategory: [formData.selectedCategory],
      })
      await sendEmailVerificationLink(formData.email)
      setCurrentStep(4)
    } catch (error: any) {
      setIsSubmitting(false)
      toast.error(error.message, {
        icon: <div className="text-red-500">✕</div>,
      })
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addSocialHandle = () => {
    setFormData(prev => ({
      ...prev,
      socialHandles: [...prev.socialHandles, { platform: '', username: '' }],
    }))
  }

  const removeSocialHandle = (index: number) => {
    setFormData(prev => ({
      ...prev,
      socialHandles: prev.socialHandles.filter((_, i) => i !== index),
    }))
  }

  const updateSocialHandle = (
    index: number,
    field: keyof SocialHandle,
    value: string
  ) => {
    setFormData(prev => ({
      ...prev,
      socialHandles: prev.socialHandles.map((handle, i) =>
        i === index ? { ...handle, [field]: value } : handle
      ),
    }))
  }

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Social step continue button logic
  const canContinueSocial =
    formData.socialHandles.length === 1
      ? formData.socialHandles[0].platform &&
        formData.socialHandles[0].username.trim() !== ''
      : formData.socialHandles.every(
          h => h.platform && h.username.trim() !== ''
        )

  return (
    <div className="min-h-screen flex flex-col justify-start bg-white px-2 py-14">
      <h1 className="text-center text-3xl xs:text-4xl md:text-5xl font-bold mb-8">
        Apply to be a <span className="text-[#1ade85]">Critiquer</span>
      </h1>
      <div className="h-8" />
      <div className="w-full max-w-4xl mx-auto">
        {/* Stepper */}
        {currentStep !== 4 && (
          <div className="rounded-t-2xl bg-green-700 flex items-center justify-between px-6 sm:px-12 py-4 relative">
            <div className="w-full max-w-xl mx-auto flex items-center justify-between">
              {stepIcons.map((Icon, idx) => (
                <React.Fragment key={idx}>
                  <div className="flex flex-col items-center z-10">
                    <div className="w-[27px] h-[27px] sm:w-8 sm:h-8 flex items-center justify-center rounded-full bg-white border-4 border-white shadow-md">
                      <Icon
                        className={`w-5 h-5 sm:w-7 sm:h-7 ${currentStep >= idx + 1 ? 'text-primary' : 'text-gray-400'}`}
                      />
                    </div>
                    <span
                      className={`mt-2 text-sm font-medium ${currentStep === idx + 1 ? 'text-white' : 'text-green-100'}`}
                    >
                      {stepLabels[idx]}
                    </span>
                  </div>
                  {/* Progress bar except after last icon */}
                  {idx < stepIcons.length - 1 && (
                    <div className="flex-1 flex items-center">
                      <div
                        className="h-[1px] bg-white"
                        style={{
                          width: '100%',
                          alignSelf: 'center',
                          marginTop: '-18px',
                        }}
                      />
                    </div>
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>
        )}
        {/* Card */}
        <div className="rounded-b-2xl rounded-t-none bg-[#eaf7f0] shadow-lg px-4 sm:px-8 py-8 pb-12">
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Step 1: Information */}
            {currentStep === 1 && (
              <>
                <Input
                  placeholder="First Name*"
                  value={formData.firstName}
                  onChange={e => handleInputChange('firstName', e.target.value)}
                  required
                />
                <Input
                  placeholder="Last Name*"
                  value={formData.lastName || ''}
                  onChange={e => handleInputChange('lastName', e.target.value)}
                  required
                />
                <div>
                  <Input
                    placeholder="Email address*"
                    type="email"
                    value={formData.email}
                    onChange={e => {
                      handleInputChange('email', e.target.value)
                      setEmailError('')
                    }}
                    required
                  />
                  {emailError && (
                    <div className="text-red-600 text-xs mt-1">
                      {emailError}
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2 mt-2">
                  <input
                    type="checkbox"
                    id="terms"
                    checked={termsAccepted}
                    onChange={e => setTermsAccepted(e.target.checked)}
                    required
                    className="accent-green-600 w-5 h-5"
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-gray-700 align-middle"
                  >
                    By clicking Create account, I agree that I have read and
                    accepted the{' '}
                    <a href="/terms" className="text-green-700 underline">
                      Terms of Service
                    </a>
                    , and{' '}
                    <a href="/privacy" className="text-green-700 underline">
                      Privacy Policy
                    </a>
                    .
                  </label>
                </div>
                <Button
                  type="button"
                  className={`w-full mt-4 ${formData.firstName && formData.lastName && formData.email && termsAccepted ? 'bg-black text-white hover:bg-gray-900' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
                  disabled={
                    !(
                      formData.firstName &&
                      formData.lastName &&
                      formData.email &&
                      termsAccepted
                    )
                  }
                  onClick={() => {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                    if (!emailRegex.test(formData.email)) {
                      setEmailError('Invalid email address')
                      return
                    }
                    setEmailError('')
                    nextStep()
                  }}
                >
                  Continue
                </Button>
              </>
            )}
            {/* Step 2: Social */}
            {currentStep === 2 && (
              <>
                <div className="mb-4">
                  <h2 className="text-xl font-semibold text-center mb-2">
                    Tell us about your Social Channels
                  </h2>
                  <p className="text-center text-gray-600 mb-4 text-sm">
                    Help us learn more about you and your audience by sharing
                    your social channels.
                  </p>
                  <div className="mb-4 space-y-6">
                    {formData.socialHandles.map((handle, index) => (
                      <div key={index} className="relative">
                        <div className="flex items-center mb-2">
                          <div className="w-full">
                            <Select
                              value={handle.platform}
                              onValueChange={value =>
                                updateSocialHandle(index, 'platform', value)
                              }
                            >
                              <SelectTrigger
                                className="w-full h-10"
                                style={{ border: '0.88px solid #DCDCDC' }}
                              >
                                <SelectValue
                                  placeholder="Select Platform"
                                  className="text-left"
                                >
                                  {handle.platform &&
                                    (() => {
                                      const selectedPlatform =
                                        socialPlatforms.find(
                                          p => p.name === handle.platform
                                        )
                                      const IconComponent =
                                        selectedPlatform?.icon
                                      return (
                                        <div className="flex items-center gap-2">
                                          {IconComponent && (
                                            <IconComponent
                                              className="h-4 w-4"
                                              style={{
                                                color: selectedPlatform?.color,
                                              }}
                                            />
                                          )}
                                          <span className="text-sm truncate">
                                            {handle.platform}
                                          </span>
                                        </div>
                                      )
                                    })()}
                                </SelectValue>
                              </SelectTrigger>
                              <SelectContent className="max-h-44 z-[10000] overflow-y-auto">
                                {socialPlatforms.map(platform => {
                                  const IconComponent = platform.icon
                                  const isSelected =
                                    handle.platform === platform.name
                                  return (
                                    <SelectItem
                                      key={platform.name}
                                      value={platform.name}
                                      className={`w-full cursor-pointer transition-colors duration-200 ${isSelected ? 'bg-primary/10 border-l-2 border-primary text-primary font-medium' : 'hover:bg-gray-50 focus:bg-gray-50'}`}
                                    >
                                      <div className="flex flex-row items-center justify-start gap-3 py-2 px-1">
                                        {IconComponent && (
                                          <IconComponent
                                            className="h-4 w-4 flex-shrink-0"
                                            style={{ color: platform.color }}
                                          />
                                        )}
                                        <div
                                          className={`flex-1 text-sm ${isSelected ? 'font-medium' : ''}`}
                                        >
                                          {platform.name}
                                        </div>
                                      </div>
                                    </SelectItem>
                                  )
                                })}
                              </SelectContent>
                            </Select>
                          </div>
                          {formData.socialHandles.length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => removeSocialHandle(index)}
                              className="ml-2 rounded-full border border-gray-300 hover:bg-gray-200 transition-colors"
                              aria-label="Remove social handle"
                            >
                              <X className="h-4 w-4 text-gray-500" />
                            </Button>
                          )}
                        </div>
                        <div className="flex">
                          <span className="inline-flex items-center px-3 text-sm text-gray-900 bg-gray-200 border border-r-0 border-gray-300 rounded-l-md h-10">
                            @
                          </span>
                          <Input
                            style={{ border: '0.88px solid #DCDCDC' }}
                            value={handle.username}
                            onChange={e =>
                              updateSocialHandle(
                                index,
                                'username',
                                e.target.value
                              )
                            }
                            className="rounded-l-none h-10 flex-1"
                            placeholder="username"
                            autoCapitalize="none"
                            autoCorrect="off"
                            spellCheck="false"
                            required
                          />
                          {formData.socialHandles.length > 1 && (
                            <div className="w-[46px]" />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addSocialHandle}
                    className="w-full flex items-center bg-accent justify-center gap-2 mb-8 mt-8"
                  >
                    <Plus className="h-5 w-5" /> Add another platform
                  </Button>
                  <div className="bg-green-100 text-green-800 rounded-lg p-3 text-sm flex items-center gap-2">
                    <Check className="h-5 w-5 text-green-600" />A public social
                    media account is required to be eligible for Critiql
                    Critiquer.
                  </div>
                </div>
                <div className="flex gap-3 mt-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={prevStep}
                  >
                    Back
                  </Button>
                  <Button
                    type="button"
                    className={`flex-1 ${canContinueSocial ? 'bg-black text-white hover:bg-gray-900' : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
                    disabled={!canContinueSocial}
                    onClick={canContinueSocial ? nextStep : undefined}
                  >
                    Continue
                  </Button>
                </div>
              </>
            )}
            {/* Step 3: Niche */}
            {currentStep === 3 && (
              <>
                <div className="mb-4">
                  <h2 className="text-xl font-semibold text-center mb-2">
                    Select your Niche
                  </h2>
                  <p className="text-center text-gray-600 mb-4 text-sm">
                    Choose the category that best fits your content to match you
                    with relevant brands and campaigns.
                  </p>
                  {!categoriesError ? (
                    <Select
                      value={formData.selectedCategory}
                      onValueChange={value =>
                        setFormData(prev => ({
                          ...prev,
                          selectedCategory: value,
                        }))
                      }
                      // disabled={categoriesLoading || categoriesError}
                    >
                      <SelectTrigger
                        className="w-full h-10"
                        style={{ border: '0.88px solid #DCDCDC' }}
                      >
                        <SelectValue
                          placeholder="Select Category"
                          className="text-left"
                        >
                          {categories?.find(
                            cat => cat.id === formData.selectedCategory
                          )?.categoryName || ''}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent className="max-h-44 z-[10000] overflow-y-auto">
                        {categories && categories.length > 0 ? (
                          categories.map(category => (
                            <SelectItem
                              key={category.id}
                              value={category.id}
                              className="w-full cursor-pointer transition-colors duration-200 hover:bg-gray-50 focus:bg-gray-50"
                            >
                              <div className="flex flex-row items-center justify-start gap-3 py-2 px-1">
                                <div className="flex-1 text-sm">
                                  {category.categoryName}
                                </div>
                              </div>
                            </SelectItem>
                          ))
                        ) : (
                          <div className="px-4 py-2 text-gray-500 text-sm">
                            No categories found
                          </div>
                        )}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-red-500 mt-2">
                      {categoriesError}
                    </p>
                  )}
                </div>
                <div className="flex gap-3 mt-4">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={prevStep}
                  >
                    Back
                  </Button>
                  <Button
                    type="submit"
                    className="flex-1 bg-black text-white hover:bg-gray-900 flex items-center justify-center"
                    disabled={isSubmitting || !formData.selectedCategory}
                  >
                    {isSubmitting ? (
                      <>
                        <svg
                          className="animate-spin h-5 w-5 mr-2 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8v8z"
                          ></path>
                        </svg>
                        Submitting...
                      </>
                    ) : (
                      <>
                        <span className="hidden sm:inline">
                          Submit Application
                        </span>
                        <span className="sm:hidden">Submit</span>
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}
            {/* Step 4: Success */}
            {currentStep === 4 && (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="rounded-full bg-green-100 p-8 mb-6">
                  <Check className="h-16 w-16 text-green-500" />
                </div>
                <h2 className="text-2xl font-bold mb-2 text-center">
                  Thanks for Applying!
                </h2>
                <h4 className="text-xl font-semibold mb-2 text-center">
                  Please check your email for verification link!
                </h4>
                <p className="text-center text-gray-700 mb-6">
                  We are reviewing your information, and you can expect a
                  response in 24 hours.
                </p>
                <Button
                  className="w-full mt-4 bg-black text-white hover:bg-gray-900"
                  onClick={() => router.push('/')}
                >
                  Continue
                </Button>
              </div>
            )}
          </form>
        </div>
      </div>
    </div>
  )
}

export default CritiqueurSignup

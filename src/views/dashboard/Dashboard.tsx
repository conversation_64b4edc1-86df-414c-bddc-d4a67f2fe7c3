'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import {
  Upload,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Star,
  TrendingUp,
  Users,
  DollarSign,
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import Layout from '@/components/shared/layout'

const Dashboard = () => {
  const { userData } = useAuth()
  const router = useRouter()
  const [submissions] = useState([
    {
      id: 1,
      title: 'Summer Fashion Collection Teaser',
      category: 'Fashion',
      status: 'completed',
      createdAt: '2024-01-15',
      critiquersRequested: 3,
      critiquersAccepted: 3,
      totalCost: 25,
      thumbnail:
        'https://www.m-fashion.de/images/uploads/collection_teaser/5300/teaser_slider_ss25_left_3-2__large.jpg',
    },
    {
      id: 2,
      title: 'Tech Product Demo Video',
      category: 'Technology',
      status: 'in_progress',
      createdAt: '2024-01-20',
      critiquersRequested: 2,
      critiquersAccepted: 1,
      totalCost: 20,
      thumbnail:
        'https://animationexplainers.com/wp-content/uploads/2021/09/product-demo-video.jpeg',
    },
    {
      id: 3,
      title: 'Food Recipe Instagram Reel',
      category: 'Food & Cooking',
      status: 'matching',
      createdAt: '2024-01-22',
      critiquersRequested: 4,
      critiquersAccepted: 0,
      totalCost: 30,
      thumbnail:
        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRjh7bnhOgHVz-MWoIkq9im4RkSGXqIEz5Ydg&s',
    },
  ])

  const [stats] = useState({
    totalSubmissions: 12,
    completedCritiques: 28,
    averageRating: 4.2,
    totalSpent: 340,
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800'
      case 'matching':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'in_progress':
        return <Clock className="h-4 w-4" />
      case 'matching':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl text-center font-bold text-gray-900 mb-2">
            {userData?.role
              ? userData.role.charAt(0).toUpperCase() + userData.role.slice(1)
              : ''}{' '}
            Dashboard
          </h1>
          {/* <p className="text-gray-600">Manage your content submissions and track feedback</p> */}
        </div>

        {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
              <Upload className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSubmissions}</div>
              <p className="text-xs text-muted-foreground">+2 from last month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Critiques</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.completedCritiques}</div>
              <p className="text-xs text-muted-foreground">From {stats.totalSubmissions} submissions</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageRating}/5.0</div>
              <p className="text-xs text-muted-foreground">Quality score</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${stats.totalSpent}</div>
              <p className="text-xs text-muted-foreground">Lifetime investment</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="submissions" className="space-y-6">
          <div className="flex justify-between items-center">
            <TabsList>
              <TabsTrigger value="submissions">My Submissions</TabsTrigger>
              <TabsTrigger value="insights">Insights</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <Button onClick={() => router.push('/upload')}>
              <Plus className="h-4 w-4 mr-2" />
              New Submission
            </Button>
          </div>

          <TabsContent value="submissions" className="space-y-6">
            <div className="grid gap-6">
              {submissions.map((submission) => (
                <Card key={submission.id}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex space-x-4">
                        <img
                          src={submission.thumbnail}
                          alt={submission.title}
                          className="w-20 h-14 object-contain rounded-md"
                        />
                        <div>
                          <CardTitle className="text-lg">{submission.title}</CardTitle>
                          <CardDescription className="flex items-center space-x-2 mt-1">
                            <Badge variant="secondary">{submission.category}</Badge>
                            <span>•</span>
                            <span>Submitted {submission.createdAt}</span>
                          </CardDescription>
                        </div>
                      </div>
                      <Badge className={getStatusColor(submission.status)}>
                        {getStatusIcon(submission.status)}
                        <span className="ml-1 capitalize">{submission.status.replace('_', ' ')}</span>
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <span className="font-medium">Critiquers:</span> {submission.critiquersAccepted}/{submission.critiquersRequested}
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Cost:</span> ${submission.totalCost}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {submission.status === "completed" && (
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View Results
                          </Button>
                        )}
                        {submission.status === "matching" && (
                          <Button variant="outline" size="sm">
                            <Users className="h-4 w-4 mr-2" />
                            View Matches
                          </Button>
                        )}
                      </div>
                    </div>

                    {submission.status !== "completed" && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress</span>
                          <span>{Math.round((submission.critiquersAccepted / submission.critiquersRequested) * 100)}%</span>
                        </div>
                        <Progress
                          value={(submission.critiquersAccepted / submission.critiquersRequested) * 100}
                          className="h-2"
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    Performance Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Most Popular Category</span>
                      <Badge>Fashion</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Best Performing Tier</span>
                      <Badge>Micro Influencers</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Average Turnaround</span>
                      <span className="text-sm font-medium">32 hours</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Feedback Highlights</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="text-sm text-green-800">"Great color palette, very engaging opening"</p>
                      <p className="text-xs text-green-600 mt-1">Fashion critique - 2 days ago</p>
                    </div>
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-800">"Consider shortening the intro for better retention"</p>
                      <p className="text-xs text-blue-600 mt-1">Tech critique - 5 days ago</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>Manage your account preferences and notifications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium mb-2">Notification Preferences</h4>
                    <div className="space-y-2">
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">Email when critiques are completed</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" defaultChecked className="rounded" />
                        <span className="text-sm">SMS for urgent updates</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" className="rounded" />
                        <span className="text-sm">Weekly performance summary</span>
                      </label>
                    </div>
                  </div>
                  <div className="pt-4">
                    <Button>Save Settings</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs> */}
      </div>
    </div>
  )
}

export default Dashboard

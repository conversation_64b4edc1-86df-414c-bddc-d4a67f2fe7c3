'use client'
import { SubmissionCard } from '@/components/dashboard/SubmissionCard'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/AuthContext'
import { useCategories } from '@/hooks/useCategories'
import { useSubmissions } from '@/hooks/useSubmissions'
import { Loader2, Plus, TrendingUp } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

const Dashboard = () => {
  const { userData } = useAuth()
  const router = useRouter()

  // Use real submissions data
  const { submissions, loading, error, hasMore, loadMore, refresh } =
    useSubmissions(10)
  const { categories } = useCategories()

  // Mock stats for now (could be calculated from submissions data)
  const [stats] = useState({
    totalSubmissions: submissions.length,
    completedCritiques: 28,
    averageRating: 4.2,
    totalSpent: 340,
  })

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl text-center font-bold text-gray-900 mb-2">
          {userData?.role
            ? userData.role.charAt(0).toUpperCase() + userData.role.slice(1)
            : ''}{' '}
          Dashboard
        </h1>
      </div>

      {/* Stats Cards - Commented out for now */}
      {/* <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
                <Upload className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalSubmissions}</div>
                <p className="text-xs text-muted-foreground">+2 from last month</p>
              </CardContent>
            </Card>
          </div> */}

      <Tabs defaultValue="submissions" className="space-y-6">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="submissions">My Submissions</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>
          <Button onClick={() => router.push('/upload')}>
            <Plus className="h-4 w-4 mr-2" />
            New Submission
          </Button>
        </div>

        <TabsContent value="submissions" className="space-y-6">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
              <span className="ml-2 text-gray-500">Loading submissions...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">{error}</p>
              <Button onClick={refresh} variant="outline">
                Try Again
              </Button>
            </div>
          ) : submissions.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No submissions yet</p>
              <Button onClick={() => router.push('/upload')}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Submission
              </Button>
            </div>
          ) : (
            <div className="max-w-md mx-auto space-y-6">
              {submissions.map(submission => (
                <SubmissionCard
                  key={submission.id}
                  submission={submission}
                  categories={categories}
                />
              ))}

              {hasMore && (
                <div className="text-center">
                  <Button onClick={loadMore} variant="outline">
                    Load More
                  </Button>
                </div>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Performance Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Most Popular Category</span>
                    <Badge>Fashion</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Best Performing Tier</span>
                    <Badge>Micro Influencers</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Average Turnaround</span>
                    <span className="text-sm font-medium">32 hours</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Feedback Highlights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800">
                      "Great color palette, very engaging opening"
                    </p>
                    <p className="text-xs text-green-600 mt-1">
                      Fashion critique - 2 days ago
                    </p>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      "Consider shortening the intro for better retention"
                    </p>
                    <p className="text-xs text-blue-600 mt-1">
                      Tech critique - 5 days ago
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Account Settings</CardTitle>
              <CardDescription>
                Manage your account preferences and notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Notification Preferences</h4>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="rounded"
                      />
                      <span className="text-sm">
                        Email when critiques are completed
                      </span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        defaultChecked
                        className="rounded"
                      />
                      <span className="text-sm">SMS for urgent updates</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="checkbox" className="rounded" />
                      <span className="text-sm">
                        Weekly performance summary
                      </span>
                    </label>
                  </div>
                </div>
                <div className="pt-4">
                  <Button>Save Settings</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Dashboard

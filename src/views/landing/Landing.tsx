'use client'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import CreatorSignupModal from '@/components/modals/CreatorSignupModal'
import CritiquerSignupModal from '@/components/modals/CritiquerSignupModal'
import React from 'react'
import Hero from './hero/Hero'
import HowItWorks from './how-it-works/HowItWorks'
import TrustAndSecurity from './trus-and-security/TrustAndSecurity'
import Loading from '@/components/shared/loading'
import PricingSection from './pricing-section/PricingSection'
import { useModal } from '@/contexts/ModalContext'

const Landing = () => {
  const {
    isCreatorModalOpen,
    setCreatorModalOpen,
    critiquerModalOpen,
    setCritiquerModalOpen,
  } = useModal()
  const { userData, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && userData) {
      router.replace('/dashboard')
    }
  }, [userData, loading, router])

  if (loading || userData) {
    return <Loading />
  }

  return (
    <>
      <div>
        <Hero
          setCreatorModalOpen={setCreatorModalOpen}
          setCritiquerModalOpen={setCritiquerModalOpen}
        />
        <HowItWorks />
        <PricingSection setCreatorModalOpen={setCreatorModalOpen} />
        {/* <WhoAreCritiquers /> */}
        <TrustAndSecurity />
        {/* <CtaSection
                    setCreatorModalOpen={setCreatorModalOpen}
                    setCritiquerModalOpen={setCritiquerModalOpen}
                /> */}
      </div>
      {/* Modals */}
      <CreatorSignupModal
        open={isCreatorModalOpen}
        onOpenChange={setCreatorModalOpen}
      />
      <CritiquerSignupModal
        open={critiquerModalOpen}
        onOpenChange={setCritiquerModalOpen}
      />
    </>
  )
}

export default Landing

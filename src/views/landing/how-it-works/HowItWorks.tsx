import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Star, Upload, Users } from 'lucide-react'
import React from 'react'

const HowItWorks = () => {
  return (
    <section id="how-it-works" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            How It Works for Creators
          </h2>
          <p className="text-lg text-gray-600">
            Get professional feedback in three simple steps
          </p>
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          <Card className="text-center">
            <CardHeader>
              <Upload className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle>1. Upload Your Content</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Upload your video, image, or link with a brief description and
                select your content category.
              </CardDescription>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardHeader>
              <Users className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle>2. Select Feedback Tiers</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Choose from Nano, Micro, Macro, or Mega influencers based on
                your budget and needs.
              </CardDescription>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardHeader>
              <Star className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle>3. Get Actionable Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Receive detailed critiques, suggestions, and an AI-powered
                summary within 24-48 hours.
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}

export default HowItWorks

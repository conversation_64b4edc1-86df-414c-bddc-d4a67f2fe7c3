import { Badge } from '@/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import React from 'react'

const WhoAreCritiquers = () => {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">
            Who Are the Critiquers?
          </h2>
          <div className="bg-white p-8 rounded-lg shadow-sm border-l-4 border-indigo-600 max-w-4xl mx-auto">
            <p className="text-lg text-gray-700 leading-relaxed">
              Every CRITIQLE critiquer is a{' '}
              <strong>verified content creator</strong> with a public profile
              and consistently high engagement in their niche. Our team manually
              approves each application to ensure you're getting feedback from
              trusted, relevant voices — not bots or random accounts.
            </p>
          </div>
        </div>

        {/* Critiquer Tiers */}
        <div className="grid md:grid-cols-4 gap-6">
          <Card>
            <CardHeader>
              <Badge variant="secondary" className="w-fit">
                Nano
              </Badge>
              <CardTitle className="text-lg">1K - 10K</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Highly engaged micro-communities with authentic connections
              </CardDescription>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Badge
                variant="secondary"
                className="w-fit bg-blue-100 text-blue-800"
              >
                Micro
              </Badge>
              <CardTitle className="text-lg">10K - 100K</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Established creators with proven content strategies
              </CardDescription>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Badge
                variant="secondary"
                className="w-fit bg-purple-100 text-purple-800"
              >
                Macro
              </Badge>
              <CardTitle className="text-lg">100K - 1M</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Industry leaders with significant reach and influence
              </CardDescription>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Badge
                variant="secondary"
                className="w-fit bg-yellow-100 text-yellow-800"
              >
                Mega
              </Badge>
              <CardTitle className="text-lg">1M+</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Top-tier influencers with massive audiences
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}

export default WhoAreCritiquers

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Check } from 'lucide-react'
import React from 'react'

interface PricingSectionProps {
  setCreatorModalOpen: (open: boolean) => void
}

const PricingSection = ({ setCreatorModalOpen }: PricingSectionProps) => {
  return (
    <section id="pricing" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Choose Your Feedback Package
          </h2>
          <p className="text-lg text-gray-600">
            Get insights from verified influencers in your niche
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          {/* Micro-Influencer Pack */}
          <Card className="relative border-2 hover:border-indigo-300 transition-colors">
            <CardHeader className="text-center">
              <Badge
                variant="secondary"
                className="w-fit mx-auto mb-2bg-blue-100 text-blue-800"
              >
                Most Popular
              </Badge>
              <CardTitle className="text-2xl">Micro-Influencer Pack</CardTitle>
              <div className="text-3xl font-bold text-primary mt-2">$375</div>
              <CardDescription className="text-sm mt-2">
                Trusted influencers with 10K–50K followers and strong niche
                engagement
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>3 detailed critiques</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>Actionable insights</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>Overall summary report</span>
                </div>
              </div>
              <Button
                className="w-full mt-6"
                onClick={() => setCreatorModalOpen(true)}
              >
                Get Started
              </Button>
            </CardContent>
          </Card>

          {/* Mid-Tier Influencer Pack */}
          <Card className="relative border-2 hover:border-indigo-300 transition-colors">
            <CardHeader className="text-center">
              <Badge
                variant="secondary"
                className="w-fit mx-auto mb-2 bg-green-100 text-green-800"
              >
                Best Value
              </Badge>
              <CardTitle className="text-2xl">
                Mid-Tier Influencer Pack
              </CardTitle>
              <div className="text-3xl font-bold text-primary mt-2">$550</div>
              <CardDescription className="text-sm mt-2">
                Proven creators with 50K–150K followers and a history of
                successful content strategies
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>3 detailed critiques</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>Actionable insights</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>Overall summary report</span>
                </div>
              </div>
              <Button
                className="w-full mt-6"
                onClick={() => setCreatorModalOpen(true)}
              >
                Get Started
              </Button>
            </CardContent>
          </Card>

          {/* Macro-Influencer Pack */}
          <Card className="relative border-2 hover:border-indigo-300 transition-colors">
            <CardHeader className="text-center">
              <Badge
                variant="secondary"
                className="w-fit mx-auto mb-2 bg-purple-100 text-purple-800"
              >
                Premium
              </Badge>
              <CardTitle className="text-2xl">Macro-Influencer Pack</CardTitle>
              <div className="text-3xl font-bold text-primary mt-2">$725</div>
              <CardDescription className="text-sm mt-2">
                Influencers with 150K+ followers and high-impact reach in your
                category
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>3 detailed critiques</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>Actionable insights</span>
                </div>
                <div className="flex items-center gap-3">
                  <Check className="h-5 w-5 text-green-600" />
                  <span>Overall summary report</span>
                </div>
              </div>
              <Button
                className="w-full mt-6"
                onClick={() => setCreatorModalOpen(true)}
              >
                Get Started
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  )
}

export default PricingSection

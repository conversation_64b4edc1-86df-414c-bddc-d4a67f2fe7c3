import { Button } from '@/components/ui/button'
import { ArrowRight, Users } from 'lucide-react'
import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface HeroProps {
  setCreatorModalOpen: (open: boolean) => void
  setCritiquerModalOpen: (open: boolean) => void
}

const Hero = ({ setCreatorModalOpen, setCritiquerModalOpen }: HeroProps) => {
  return (
    <section className="relative min-h-[calc(100vh+64px)] flex items-center justify-center bg-white pt-0">
      <div className="absolute inset-0 bg-[linear-gradient(0.06deg,rgba(0,0,0,0.704)_10.85%,rgba(0,0,0,0)_90.83%)] z-10 xs:bg-none" />

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-8 lg:px-8 xl:px-14 relative z-10 pt-60 xs:pt-0 pb-4">
        <div className="w-full xs:w-1/2 2xl:w-[75%] text-center xs:text-left">
          <h1 className="text-3xl xs:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white xs:text-black ">
            Get Expert Feedback from{' '}
            <span className="text-[#1ade85] xs:text-primary">
              Verified Influencers
            </span>
          </h1>
          <div className="flex flex-col md:flex-row gap-4 justify-center md:justify-start">
            <Link href="/creator-signup">
              <Button
                size="lg"
                className="w-full text-lg px-8 py-4"
                // onClick={() => setCreatorModalOpen(true)}
              >
                Get Feedback
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/critiquer-signup">
              <Button
                size="lg"
                className="w-full  border-black text-lg px-8 py-4 bg-white text-black hover:bg-green-600 hover:text-white transition-colors shadow-[0px_3px_12px_0px_#00000066] hover:shadow-[0px_3px_12px_0px_#00000066] transition-all"
                // onClick={() => setCritiquerModalOpen(true)}
              >
                Become a Critiquer
                <Users className="ml-2 h-5 w-5" />
              </Button>
            </Link>{' '}
          </div>
        </div>
      </div>

      {/* image Container */}
      <div className="absolute xs:right-4 md:right-10 lg:right-20 xl:right-40 inset-0 flex items-center justify-end pt-5">
        <div className="relative w-full h-full">
          <Image
            src="/images/hero-image.png"
            alt="Hero background"
            fill
            priority
            quality={100}
            className="object-contain object-center xs:object-right"
          />
        </div>
      </div>
    </section>
  )
}

export default Hero

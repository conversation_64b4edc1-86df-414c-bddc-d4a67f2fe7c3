import { CheckCir<PERSON>, <PERSON>, Shield } from 'lucide-react'
import React from 'react'

const TrustAndSecurity = () => {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Trust & Security
          </h2>
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center">
            <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              All Reviewers Manually Verified
            </h3>
            <p className="text-gray-600">
              Every critiquer is vetted by our team to ensure authenticity and
              expertise
            </p>
          </div>
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              Payment Held Until Commitment
            </h3>
            <p className="text-gray-600">
              You're only charged after critiquers accept your request
            </p>
          </div>
          <div className="text-center">
            <Clock className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              Content Never Shared Publicly
            </h3>
            <p className="text-gray-600">
              Your content remains private and is only seen by selected
              critiquers
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TrustAndSecurity

import { Button } from '@/components/ui/button'
import React from 'react'

interface CtaSectionProps {
  setCreatorModalOpen: (open: boolean) => void
  setCritiquerModalOpen: (open: boolean) => void
}

const CtaSection = ({
  setCreatorModalOpen,
  setCritiquerModalOpen,
}: CtaSectionProps) => {
  return (
    <section className="py-16 bg-indigo-600">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-3xl font-bold text-white mb-4">
          Ready to Get Started?
        </h2>
        <p className="text-xl text-indigo-100 mb-8">
          Join thousands of creators getting authentic feedback from verified
          voices
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            size="lg"
            variant="outline"
            className="bg-white text-indigo-600 hover:bg-gray-100 border-indigo-600"
            onClick={() => setCreatorModalOpen(true)}
          >
            Submit Your First Content
          </Button>
          <Button
            size="lg"
            variant="outline"
            // className="border-white text-indigo-600 hover:bg-gray-100"
            className="bg-white text-indigo-600 hover:bg-gray-100 border-indigo-600"
            onClick={() => setCritiquerModalOpen(true)}
          >
            Apply to Become a Critiquer
          </Button>
        </div>
      </div>
    </section>
  )
}

export default CtaSection

module.exports = {
  extends: ['next/core-web-vitals', 'prettier'],
  rules: {
    // React rules
    'react/react-in-jsx-scope': 'off', // Not needed in Next.js 13+
    'react/prop-types': 'off', // Using TypeScript
    'react/no-unescaped-entities': 'warn',

    // General rules
    'no-console': 'warn',
    'no-debugger': 'error',
    'prefer-const': 'error',
    'no-var': 'error',

    // Code style
    quotes: ['error', 'single', { avoidEscape: true }],
    semi: ['error', 'never'],
    'comma-dangle': ['error', 'always-multiline'],
    'object-curly-spacing': ['error', 'always'],
    'array-bracket-spacing': ['error', 'never'],
  },
  ignorePatterns: [
    '.next/**',
    'node_modules/**',
    'out/**',
    'build/**',
    'dist/**',
    '*.config.js',
    '*.config.ts',
  ],
}
